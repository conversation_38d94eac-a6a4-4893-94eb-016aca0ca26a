<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тестирование Фазы 4 - Расширенное Fingerprinting</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            color: #ffffff;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        h1 {
            text-align: center;
            color: #ffa500;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 0 0 20px rgba(255, 165, 0, 0.5);
        }
        
        .test-section {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #ffa500;
        }
        
        .test-button {
            background: linear-gradient(45deg, #ffa500, #ff8c00);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            margin: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(255, 165, 0, 0.3);
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 165, 0, 0.4);
        }
        
        .test-button:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        
        .status.success {
            background: rgba(0, 255, 0, 0.2);
            border: 1px solid #00ff00;
            color: #00ff00;
        }
        
        .status.error {
            background: rgba(255, 0, 0, 0.2);
            border: 1px solid #ff0000;
            color: #ff0000;
        }
        
        .status.warning {
            background: rgba(255, 255, 0, 0.2);
            border: 1px solid #ffff00;
            color: #ffff00;
        }
        
        .status.info {
            background: rgba(0, 150, 255, 0.2);
            border: 1px solid #0096ff;
            color: #0096ff;
        }
        
        .console-output {
            background: #000;
            color: #00ff00;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
            margin: 20px 0;
            border: 1px solid #333;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #ffa500, #ff8c00);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .component-status {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .component-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .component-card h3 {
            margin: 0 0 10px 0;
            color: #ffa500;
        }
        
        .component-card .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        
        .status-indicator.pending { background: #666; }
        .status-indicator.running { background: #ffa500; animation: pulse 1s infinite; }
        .status-indicator.success { background: #00ff00; }
        .status-indicator.error { background: #ff0000; }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .fingerprint-display {
            background: rgba(0, 0, 0, 0.5);
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            word-break: break-all;
        }
        
        .risk-score {
            font-size: 2em;
            font-weight: bold;
            text-align: center;
            margin: 20px 0;
        }
        
        .risk-low { color: #00ff00; }
        .risk-medium { color: #ffff00; }
        .risk-high { color: #ffa500; }
        .risk-critical { color: #ff0000; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛡️ Тестирование Фазы 4 - Расширенное Fingerprinting</h1>
        
        <div class="test-section">
            <h2>📊 Статус компонентов</h2>
            <div class="component-status" id="componentStatus">
                <div class="component-card">
                    <h3><span class="status-indicator pending" id="indicator-fingerprint"></span>AdvancedDeviceFingerprint</h3>
                    <div id="status-fingerprint">Ожидание...</div>
                </div>
                <div class="component-card">
                    <h3><span class="status-indicator pending" id="indicator-manager"></span>AdvancedFraudManager</h3>
                    <div id="status-manager">Ожидание...</div>
                </div>
                <div class="component-card">
                    <h3><span class="status-indicator pending" id="indicator-server"></span>Серверная интеграция</h3>
                    <div id="status-server">Ожидание...</div>
                </div>
                <div class="component-card">
                    <h3><span class="status-indicator pending" id="indicator-emulator"></span>Детекция эмуляторов</h3>
                    <div id="status-emulator">Ожидание...</div>
                </div>
                <div class="component-card">
                    <h3><span class="status-indicator pending" id="indicator-bot"></span>Детекция ботов</h3>
                    <div id="status-bot">Ожидание...</div>
                </div>
                <div class="component-card">
                    <h3><span class="status-indicator pending" id="indicator-risk"></span>Анализ рисков</h3>
                    <div id="status-risk">Ожидание...</div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>🚀 Управление тестированием</h2>
            <div style="text-align: center;">
                <button class="test-button" id="startTestBtn" onclick="startTesting()">
                    🧪 Запустить полное тестирование
                </button>
                <button class="test-button" onclick="clearConsole()">
                    🗑️ Очистить консоль
                </button>
                <button class="test-button" onclick="location.reload()">
                    🔄 Перезагрузить страницу
                </button>
            </div>
            
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div style="text-align: center; margin-top: 10px;">
                <span id="progressText">Готов к тестированию</span>
            </div>
        </div>
        
        <div class="test-section">
            <h2>📊 Результаты fingerprinting</h2>
            <div id="fingerprintResults" style="display: none;">
                <div class="fingerprint-display">
                    <strong>Fingerprint:</strong> <span id="fingerprintValue"></span>
                </div>
                <div class="risk-score">
                    Risk Score: <span id="riskScoreValue" class="risk-low">0</span>
                </div>
                <div id="componentsInfo"></div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>📋 Консоль тестирования</h2>
            <div class="console-output" id="consoleOutput">
                Консоль готова к выводу результатов тестирования...<br>
                Нажмите "Запустить полное тестирование" для начала.<br>
            </div>
        </div>
    </div>

    <!-- Подключаем необходимые скрипты -->
    <script src="js/advanced-device-fingerprint.js"></script>
    <script src="js/advanced-fraud-manager.js"></script>
    <script src="js/vpn-detector.js"></script>
    <script src="js/self-referral-detector.js"></script>
    <script src="js/fraud-blocker.js"></script>
    
    <script>
        let testInProgress = false;
        let currentTestStep = 0;
        const totalSteps = 6;
        
        // Перехватываем console.log для отображения в интерфейсе
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const originalConsoleWarn = console.warn;
        
        function addToConsole(message, type = 'log') {
            const consoleOutput = document.getElementById('consoleOutput');
            const timestamp = new Date().toLocaleTimeString();
            
            let color = '#00ff00';
            if (type === 'error') color = '#ff0000';
            if (type === 'warn') color = '#ffff00';
            if (type === 'info') color = '#0096ff';
            
            consoleOutput.innerHTML += `<span style="color: ${color}">[${timestamp}] ${message}</span><br>`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalConsoleWarn.apply(console, args);
            addToConsole(args.join(' '), 'warn');
        };
        
        function updateProgress(step, total) {
            const percentage = (step / total) * 100;
            document.getElementById('progressFill').style.width = percentage + '%';
            document.getElementById('progressText').textContent = `Шаг ${step} из ${total} (${percentage.toFixed(1)}%)`;
        }
        
        function updateComponentStatus(component, status, message = '') {
            const indicator = document.getElementById(`indicator-${component}`);
            const statusDiv = document.getElementById(`status-${component}`);
            
            indicator.className = `status-indicator ${status}`;
            statusDiv.textContent = message || status;
        }
        
        function updateFingerprintResults(fingerprint, riskScore, components) {
            document.getElementById('fingerprintResults').style.display = 'block';
            document.getElementById('fingerprintValue').textContent = fingerprint;
            
            const riskElement = document.getElementById('riskScoreValue');
            riskElement.textContent = riskScore;
            
            // Устанавливаем цвет в зависимости от уровня риска
            riskElement.className = 'risk-low';
            if (riskScore >= 85) riskElement.className = 'risk-critical';
            else if (riskScore >= 70) riskElement.className = 'risk-high';
            else if (riskScore >= 50) riskElement.className = 'risk-medium';
            
            // Отображаем информацию о компонентах
            const componentsInfo = document.getElementById('componentsInfo');
            componentsInfo.innerHTML = `<strong>Компоненты fingerprinting:</strong> ${Object.keys(components).length}`;
        }
        
        function clearConsole() {
            document.getElementById('consoleOutput').innerHTML = 'Консоль очищена...<br>';
        }
        
        async function startTesting() {
            if (testInProgress) return;
            
            testInProgress = true;
            document.getElementById('startTestBtn').disabled = true;
            document.getElementById('startTestBtn').textContent = '🔄 Тестирование...';
            
            try {
                // Мокаем Telegram WebApp
                if (typeof window.Telegram === 'undefined') {
                    window.Telegram = {
                        WebApp: {
                            initData: 'user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&auth_date=1234567890&hash=test_hash',
                            ready: () => console.log('Telegram WebApp ready'),
                            expand: () => console.log('Telegram WebApp expanded')
                        }
                    };
                }
                
                // Загружаем и запускаем тестовый скрипт
                const script = document.createElement('script');
                script.src = 'test_phase4_advanced_fingerprint.js';
                script.onload = () => {
                    console.log('Тестовый скрипт загружен и выполнен');
                };
                script.onerror = () => {
                    console.error('Ошибка загрузки тестового скрипта');
                };
                document.head.appendChild(script);
                
            } catch (error) {
                console.error('Ошибка запуска тестирования:', error);
            } finally {
                setTimeout(() => {
                    testInProgress = false;
                    document.getElementById('startTestBtn').disabled = false;
                    document.getElementById('startTestBtn').textContent = '🧪 Запустить полное тестирование';
                }, 5000);
            }
        }
        
        // Инициализация при загрузке страницы
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Страница тестирования Фазы 4 загружена');
            console.log('📋 Готов к тестированию расширенного fingerprinting');
            
            // Проверяем наличие необходимых скриптов
            setTimeout(() => {
                if (typeof window.AdvancedDeviceFingerprint !== 'undefined') {
                    updateComponentStatus('fingerprint', 'success', 'Загружен');
                } else {
                    updateComponentStatus('fingerprint', 'error', 'Не найден');
                }
                
                if (typeof window.AdvancedFraudManager !== 'undefined') {
                    updateComponentStatus('manager', 'success', 'Загружен');
                } else {
                    updateComponentStatus('manager', 'error', 'Не найден');
                }
            }, 1000);
        });
    </script>
</body>
</html>
