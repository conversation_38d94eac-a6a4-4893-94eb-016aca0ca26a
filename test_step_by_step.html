<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Пошаговый тест</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .log {
            background: #333;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>🔍 Пошаговый тест загрузки</h1>
    
    <button onclick="loadDeviceFingerprint()">1. Загрузить DeviceFingerprint</button>
    <button onclick="loadVPNDetector()">2. Загрузить VPNDetector</button>
    <button onclick="loadFraudBlocker()">3. Загрузить FraudBlocker</button>
    <button onclick="loadFraudManager()">4. Загрузить FraudManager</button>
    <button onclick="testAll()">5. Тест всех компонентов</button>
    
    <div id="log" class="log">Готов к тестированию...\n</div>

    <script>
        const logDiv = document.getElementById('log');
        
        function log(message) {
            logDiv.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function loadScript(src, checkVar) {
            return new Promise((resolve, reject) => {
                log(`Загружаем ${src}...`);
                
                const script = document.createElement('script');
                script.src = src;
                
                script.onload = () => {
                    log(`✅ ${src} загружен`);
                    
                    setTimeout(() => {
                        if (checkVar && window[checkVar]) {
                            log(`✅ ${checkVar} доступен в window`);
                            resolve();
                        } else if (checkVar) {
                            log(`❌ ${checkVar} НЕ найден в window`);
                            reject(new Error(`${checkVar} не экспортирован`));
                        } else {
                            resolve();
                        }
                    }, 100);
                };
                
                script.onerror = (error) => {
                    log(`❌ Ошибка загрузки ${src}`);
                    reject(error);
                };
                
                document.head.appendChild(script);
            });
        }
        
        async function loadDeviceFingerprint() {
            try {
                await loadScript('js/device-fingerprint.js', 'DeviceFingerprint');
                log('DeviceFingerprint готов к использованию');
            } catch (error) {
                log(`Ошибка: ${error.message}`);
            }
        }
        
        async function loadVPNDetector() {
            try {
                await loadScript('js/vpn-detector.js', 'VPNDetector');
                log('VPNDetector готов к использованию');
            } catch (error) {
                log(`Ошибка: ${error.message}`);
            }
        }
        
        async function loadFraudBlocker() {
            try {
                await loadScript('js/fraud-blocker.js', 'FraudBlocker');
                
                // Проверяем экземпляр
                if (window.fraudBlocker) {
                    log('✅ fraudBlocker экземпляр создан');
                } else {
                    log('❌ fraudBlocker экземпляр НЕ создан');
                }
                
                log('FraudBlocker готов к использованию');
            } catch (error) {
                log(`Ошибка: ${error.message}`);
            }
        }
        
        async function loadFraudManager() {
            try {
                await loadScript('js/fraud-manager.js', 'FraudManager');
                
                // Проверяем экземпляр
                if (window.fraudManager) {
                    log('✅ fraudManager экземпляр создан');
                } else {
                    log('❌ fraudManager экземпляр НЕ создан');
                }
                
                log('FraudManager готов к использованию');
            } catch (error) {
                log(`Ошибка: ${error.message}`);
            }
        }
        
        function testAll() {
            log('\n=== ФИНАЛЬНАЯ ПРОВЕРКА ===');
            
            const components = [
                'DeviceFingerprint',
                'VPNDetector', 
                'FraudBlocker',
                'FraudManager',
                'fraudBlocker',
                'fraudManager'
            ];
            
            components.forEach(comp => {
                if (window[comp]) {
                    log(`✅ ${comp} - ДОСТУПЕН`);
                } else {
                    log(`❌ ${comp} - НЕ ДОСТУПЕН`);
                }
            });
            
            // Пробуем создать экземпляры
            log('\n=== ТЕСТ СОЗДАНИЯ ЭКЗЕМПЛЯРОВ ===');
            
            try {
                if (window.DeviceFingerprint) {
                    const df = new DeviceFingerprint();
                    log('✅ DeviceFingerprint экземпляр создан');
                }
            } catch (e) {
                log(`❌ DeviceFingerprint: ${e.message}`);
            }
            
            try {
                if (window.VPNDetector) {
                    const vpn = new VPNDetector();
                    log('✅ VPNDetector экземпляр создан');
                }
            } catch (e) {
                log(`❌ VPNDetector: ${e.message}`);
            }
            
            try {
                if (window.FraudBlocker) {
                    const fb = new FraudBlocker();
                    log('✅ FraudBlocker экземпляр создан');
                }
            } catch (e) {
                log(`❌ FraudBlocker: ${e.message}`);
            }
            
            try {
                if (window.FraudManager) {
                    const fm = new FraudManager();
                    log('✅ FraudManager экземпляр создан');
                }
            } catch (e) {
                log(`❌ FraudManager: ${e.message}`);
            }
        }
        
        // Отслеживаем все ошибки JavaScript
        window.onerror = function(message, source, lineno, colno, error) {
            log(`🚨 JavaScript ошибка: ${message} в ${source}:${lineno}`);
            return false;
        };
        
        window.addEventListener('unhandledrejection', function(event) {
            log(`🚨 Unhandled Promise rejection: ${event.reason}`);
        });
    </script>
</body>
</html>
