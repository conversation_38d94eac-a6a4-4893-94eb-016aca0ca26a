/**
 * Упрощенный тест Фазы 3 - Проверка интеграции самореферралов
 */

console.log('🚀 ТЕСТ ФАЗЫ 3 - СИСТЕМА ДЕТЕКЦИИ САМОРЕФЕРРАЛОВ');
console.log('=' .repeat(60));

// Простая проверка загрузки модулей
const fs = require('fs');
const path = require('path');

try {
    // Проверяем наличие файлов
    const files = [
        'js/self-referral-detector.js',
        'js/fraud-manager.js',
        'api/fraud-detection.php'
    ];
    
    console.log('\n📁 ПРОВЕРКА ФАЙЛОВ:');
    let filesOk = 0;
    
    files.forEach(file => {
        if (fs.existsSync(file)) {
            console.log(`✅ ${file} - найден`);
            filesOk++;
        } else {
            console.log(`❌ ${file} - не найден`);
        }
    });
    
    console.log(`\n📊 Файлов найдено: ${filesOk}/${files.length}`);
    
    // Проверяем содержимое SelfReferralDetector
    console.log('\n🔍 ПРОВЕРКА SELF-REFERRAL-DETECTOR:');
    const detectorCode = fs.readFileSync('js/self-referral-detector.js', 'utf8');
    
    const checks = [
        { name: 'Класс SelfReferralDetector', pattern: /class SelfReferralDetector/ },
        { name: 'Метод analyzeReferralRelations', pattern: /analyzeReferralRelations/ },
        { name: 'Метод extractUserIdFromInitData', pattern: /extractUserIdFromInitData/ },
        { name: 'API запрос к fraud-detection.php', pattern: /\/api\/fraud-detection\.php/ },
        { name: 'Экспорт в window', pattern: /window\.SelfReferralDetector/ }
    ];
    
    let checksOk = 0;
    checks.forEach(check => {
        if (check.pattern.test(detectorCode)) {
            console.log(`✅ ${check.name} - найден`);
            checksOk++;
        } else {
            console.log(`❌ ${check.name} - не найден`);
        }
    });
    
    console.log(`\n📊 Проверок пройдено: ${checksOk}/${checks.length}`);
    
    // Проверяем FraudManager
    console.log('\n🔍 ПРОВЕРКА FRAUD-MANAGER:');
    const fraudManagerCode = fs.readFileSync('js/fraud-manager.js', 'utf8');
    
    const fraudChecks = [
        { name: 'Класс FraudManager', pattern: /class FraudManager/ },
        { name: 'Метод checkSelfReferrals', pattern: /checkSelfReferrals/ },
        { name: 'Интеграция с SelfReferralDetector', pattern: /SelfReferralDetector/ },
        { name: 'Проверка самореферралов в initialize', pattern: /checkSelfReferrals.*initData/ }
    ];
    
    let fraudChecksOk = 0;
    fraudChecks.forEach(check => {
        if (check.pattern.test(fraudManagerCode)) {
            console.log(`✅ ${check.name} - найден`);
            fraudChecksOk++;
        } else {
            console.log(`❌ ${check.name} - не найден`);
        }
    });
    
    console.log(`\n📊 Проверок FraudManager: ${fraudChecksOk}/${fraudChecks.length}`);
    
    // Проверяем API
    console.log('\n🔍 ПРОВЕРКА API FRAUD-DETECTION:');
    const apiCode = fs.readFileSync('api/fraud-detection.php', 'utf8');
    
    const apiChecks = [
        { name: 'Action analyze_referrals', pattern: /case 'analyze_referrals'/ },
        { name: 'Функция analyzeReferrals', pattern: /function analyzeReferrals/ },
        { name: 'Функция checkCyclicReferrals', pattern: /function checkCyclicReferrals/ },
        { name: 'Функция checkFingerprintDuplicates', pattern: /function checkFingerprintDuplicates/ },
        { name: 'Логирование фрода', pattern: /function logFraudAnalysis/ }
    ];
    
    let apiChecksOk = 0;
    apiChecks.forEach(check => {
        if (check.pattern.test(apiCode)) {
            console.log(`✅ ${check.name} - найден`);
            apiChecksOk++;
        } else {
            console.log(`❌ ${check.name} - не найден`);
        }
    });
    
    console.log(`\n📊 Проверок API: ${apiChecksOk}/${apiChecks.length}`);
    
    // Итоговая оценка
    const totalChecks = files.length + checks.length + fraudChecks.length + apiChecks.length;
    const totalPassed = filesOk + checksOk + fraudChecksOk + apiChecksOk;
    
    console.log('\n📈 ИТОГОВЫЕ РЕЗУЛЬТАТЫ:');
    console.log('=' .repeat(60));
    console.log(`Всего проверок: ${totalChecks}`);
    console.log(`Пройдено: ${totalPassed}`);
    console.log(`Процент готовности: ${Math.round((totalPassed / totalChecks) * 100)}%`);
    
    if (totalPassed === totalChecks) {
        console.log('\n🎉 ФАЗА 3 ПОЛНОСТЬЮ ГОТОВА!');
        console.log('✅ Все компоненты системы детекции самореферралов на месте');
        console.log('✅ Интеграция с FraudManager выполнена');
        console.log('✅ API endpoints созданы');
        console.log('✅ Система готова к работе');
        console.log('\n🚀 МОЖНО ПЕРЕХОДИТЬ К ФАЗЕ 4!');
    } else if (totalPassed >= totalChecks * 0.8) {
        console.log('\n⚠️ ФАЗА 3 ПОЧТИ ГОТОВА');
        console.log('✅ Основные компоненты на месте');
        console.log('⚠️ Есть несколько мелких недочетов');
        console.log('🔧 Рекомендуется финальная проверка');
    } else {
        console.log('\n❌ ФАЗА 3 НЕ ГОТОВА');
        console.log('❌ Критические компоненты отсутствуют');
        console.log('🔧 Требуется дополнительная работа');
    }
    
} catch (error) {
    console.error('\n❌ ОШИБКА ТЕСТИРОВАНИЯ:', error.message);
}

console.log('\n' + '='.repeat(60));
console.log('🏁 ПРОВЕРКА ЗАВЕРШЕНА');
