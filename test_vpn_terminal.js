/**
 * Терминальный тест VPN блокировки
 * Запуск: node test_vpn_terminal.js
 */

console.log('🧪 Терминальный тест VPN блокировки');
console.log('=====================================');

// Симулируем браузерное окружение
global.window = {
    console: console,
    fetch: async (url, options) => {
        console.log(`🌐 Мок fetch запрос: ${url}`);
        return {
            ok: true,
            json: async () => ({
                success: true,
                fraud_status: {
                    blocked: false,
                    fraud_detected: false
                }
            })
        };
    }
};

global.document = {
    createElement: () => ({
        style: {},
        appendChild: () => {},
        addEventListener: () => {}
    }),
    body: {
        appendChild: () => {},
        style: {}
    },
    getElementById: () => null,
    querySelectorAll: () => []
};

// Загружаем модули
try {
    // Симулируем DeviceFingerprint
    global.window.DeviceFingerprint = class DeviceFingerprint {
        async generateFingerprint() {
            console.log('📱 Мок DeviceFingerprint: генерируем отпечаток...');
            return {
                fingerprint: 'test_fingerprint_123',
                components: {
                    canvas: 'test_canvas',
                    webgl: 'test_webgl'
                }
            };
        }
    };

    // Симулируем VPNDetector
    global.window.VPNDetector = class VPNDetector {
        async detectVPN() {
            console.log('🔍 Мок VPNDetector: обнаруживаем VPN...');
            return {
                is_vpn_detected: true,
                risk_score: 85,
                indicators: ['webrtc_leak', 'timing_anomaly'],
                details: {
                    webrtc_ips: ['********'],
                    timing_score: 45
                }
            };
        }
    };

    // Симулируем FraudBlocker
    global.window.FraudBlocker = class FraudBlocker {
        constructor() {
            this.isBlocked = false;
        }

        showBlockScreen(reason) {
            console.log(`🚫 FraudBlocker: показываем экран блокировки для "${reason}"`);
            this.isBlocked = true;
            return true;
        }

        showWarningScreen(reason) {
            console.log(`⚠️ FraudBlocker: показываем предупреждение для "${reason}"`);
            return true;
        }

        async checkFraudStatus() {
            console.log('🔍 FraudBlocker: проверяем статус фрода...');
            return false;
        }
    };

    // Создаем глобальный экземпляр FraudBlocker
    global.window.fraudBlocker = new global.window.FraudBlocker();

    // Симулируем FraudManager
    global.window.FraudManager = class FraudManager {
        constructor() {
            this.initialized = false;
            this.deviceFingerprint = null;
            this.fraudBlocker = null;
            this.fingerprintData = null;
            this.userBlocked = false;
            this.vpnDetector = null;
            this.vpnResults = null;
            
            console.log('[FraudManager] 🛡️ Инициализация главного менеджера антифрод системы');
        }

        async initialize(initData) {
            try {
                console.log('[FraudManager] 🚀 Запуск антифрод системы...');
                
                // Проверяем зависимости
                if (!global.window.DeviceFingerprint) {
                    throw new Error('DeviceFingerprint модуль не загружен');
                }

                if (!global.window.fraudBlocker) {
                    throw new Error('FraudBlocker модуль не загружен');
                }

                if (!global.window.VPNDetector) {
                    throw new Error('VPNDetector модуль не загружен');
                }

                this.deviceFingerprint = new global.window.DeviceFingerprint();
                this.fraudBlocker = global.window.fraudBlocker;
                this.vpnDetector = new global.window.VPNDetector();
                
                // 1. Сначала проверяем текущий статус фрода
                console.log('[FraudManager] 🔍 Проверяем текущий статус фрода...');
                const isCurrentlyBlocked = await this.fraudBlocker.checkFraudStatus();
                
                if (isCurrentlyBlocked) {
                    console.log('[FraudManager] 🚫 Пользователь уже заблокирован');
                    this.userBlocked = true;
                    return false;
                }
                
                // 2. Генерируем отпечаток устройства
                console.log('[FraudManager] 📱 Генерируем отпечаток устройства...');
                this.fingerprintData = await this.deviceFingerprint.generateFingerprint();
                
                // 3. Проверяем VPN
                console.log('[FraudManager] 🌐 Проверяем VPN/Proxy...');
                this.vpnResults = await this.vpnDetector.detectVPN();
                
                console.log('[FraudManager] 📊 Результаты VPN детекции:', {
                    vpn_detected: this.vpnResults.is_vpn_detected,
                    risk_score: this.vpnResults.risk_score,
                    indicators: this.vpnResults.indicators
                });

                // 3.1. Проверяем VPN и блокируем если обнаружен
                if (this.vpnResults.is_vpn_detected) {
                    console.log('[FraudManager] 🚫 VPN обнаружен, блокируем пользователя');

                    // Показываем экран блокировки VPN
                    this.fraudBlocker.showBlockScreen('vpn_detected');

                    this.userBlocked = true;
                    return false;
                }

                console.log('[FraudManager] ✅ Антифрод система инициализирована успешно');
                this.initialized = true;
                return true;
                
            } catch (error) {
                console.error('[FraudManager] ❌ Ошибка инициализации:', error.message);
                return false;
            }
        }
    };

    console.log('✅ Все модули загружены');

} catch (error) {
    console.error('❌ Ошибка загрузки модулей:', error.message);
    process.exit(1);
}

// Запускаем тесты
async function runTests() {
    console.log('\n🧪 Запускаем тесты...\n');

    try {
        // Тест 1: Проверка загрузки компонентов
        console.log('📋 Тест 1: Проверка загрузки компонентов');
        console.log('DeviceFingerprint:', !!global.window.DeviceFingerprint ? '✅' : '❌');
        console.log('VPNDetector:', !!global.window.VPNDetector ? '✅' : '❌');
        console.log('FraudBlocker:', !!global.window.FraudBlocker ? '✅' : '❌');
        console.log('FraudManager:', !!global.window.FraudManager ? '✅' : '❌');
        console.log('fraudBlocker instance:', !!global.window.fraudBlocker ? '✅' : '❌');

        // Тест 2: Создание FraudManager
        console.log('\n📋 Тест 2: Создание FraudManager');
        const manager = new global.window.FraudManager();
        console.log('FraudManager создан:', !!manager ? '✅' : '❌');

        // Тест 3: Инициализация с VPN
        console.log('\n📋 Тест 3: Инициализация FraudManager с VPN');
        const testInitData = 'user=%7B%22id%22%3A123456789%2C%22first_name%22%3A%22TestUser%22%7D&auth_date=1234567890&hash=test';
        
        const result = await manager.initialize(testInitData);
        
        console.log('\n📊 Результаты теста:');
        console.log('Результат инициализации:', result ? '✅ true' : '❌ false');
        console.log('Пользователь заблокирован:', manager.userBlocked ? '✅ true' : '❌ false');
        console.log('VPN обнаружен:', manager.vpnResults?.is_vpn_detected ? '✅ true' : '❌ false');

        // Ожидаемый результат: result = false, userBlocked = true
        if (!result && manager.userBlocked) {
            console.log('\n🎉 ТЕСТ ПРОЙДЕН: Пользователь корректно заблокирован из-за VPN');
        } else {
            console.log('\n❌ ТЕСТ НЕ ПРОЙДЕН: Логика блокировки работает неправильно');
            console.log('Ожидалось: result=false, userBlocked=true');
            console.log(`Получено: result=${result}, userBlocked=${manager.userBlocked}`);
        }

    } catch (error) {
        console.error('\n❌ Ошибка во время тестов:', error.message);
        console.error(error.stack);
    }
}

// Запускаем тесты
runTests().then(() => {
    console.log('\n✅ Тестирование завершено');
}).catch(error => {
    console.error('\n❌ Критическая ошибка:', error.message);
    process.exit(1);
});
