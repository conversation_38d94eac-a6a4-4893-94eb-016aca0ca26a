<?php
/**
 * api/fraud-detection.php
 * Серверная система антифрод детекции
 * 
 * Обрабатывает отпечатки устройств, анализирует подозрительную активность
 * и принимает решения о блокировке пользователей
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Проверяем существование файлов перед подключением
$required_files = [
    __DIR__ . '/config.php',
    __DIR__ . '/db_mock.php',
    __DIR__ . '/security.php'
];

foreach ($required_files as $file) {
    if (!file_exists($file)) {
        error_log("fraud-detection ERROR: Файл не найден: $file");
        http_response_code(500);
        echo json_encode(['error' => 'Ошибка сервера: отсутствует файл ' . basename($file)]);
        exit;
    }
}

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/db_mock.php';

/**
 * Проверяет, является ли пользователь администратором
 */
function isAdminUser($userId) {
    return in_array((int)$userId, ADMIN_USER_IDS);
}
require_once __DIR__ . '/security.php';

// Подключаем валидацию initData
if (file_exists(__DIR__ . '/validate_initdata.php')) {
    require_once __DIR__ . '/validate_initdata.php';
}

// Проверяем существование папки database
$database_dir = __DIR__ . '/../database';
if (!is_dir($database_dir)) {
    error_log("fraud-detection WARNING: Папка database не найдена, создаем: $database_dir");
    if (!mkdir($database_dir, 0755, true)) {
        error_log("fraud-detection ERROR: Не удалось создать папку database");
    }
}

error_log("fraud-detection INFO: Получен запрос на анализ фрода");

/**
 * Обертка для валидации initData с поддержкой тестового режима
 */
function validateInitData($initData) {
    // Проверяем тестовые данные
    if (strpos($initData, 'test_fallback_mode') === 0) {
        return [
            'id' => 12345,
            'first_name' => 'Test',
            'last_name' => 'User',
            'username' => 'testuser'
        ];
    }

    // Используем стандартную валидацию Telegram
    if (function_exists('validateTelegramInitData')) {
        $result = validateTelegramInitData($initData);
        if ($result && isset($result['user'])) {
            return $result['user'];
        }
    }

    return false;
}

try {
    // Получаем данные запроса
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input || !isset($input['action'])) {
        throw new Exception('Отсутствует параметр action');
    }

    $action = $input['action'];
    
    switch ($action) {
        case 'register_device':
            echo json_encode(registerDeviceFingerprint($input));
            break;
            
        case 'check_fraud':
            echo json_encode(checkFraudScore($input));
            break;
            
        case 'get_fraud_status':
            echo json_encode(getFraudStatus($input));
            break;

        case 'get_admin_stats':
            echo json_encode(getAdminStats());
            break;

        case 'save_admin_settings':
            echo json_encode(saveAdminSettings($input));
            break;

        case 'get_admin_settings':
            echo json_encode(getAdminSettings());
            break;

        case 'clear_fraud_log':
            echo json_encode(clearFraudLog());
            break;

        case 'unblock_device':
            echo json_encode(unblockDevice($input));
            break;

        case 'analyze_vpn':
            echo json_encode(analyzeVPN($input));
            break;

        case 'analyze_referrals':
            echo json_encode(analyzeReferrals($input));
            break;

        case 'check_fingerprint_duplicates':
            echo json_encode(checkFingerprintDuplicates($input));
            break;

        case 'get_referral_chain':
            echo json_encode(getReferralChain($input));
            break;

        case 'log_suspicious_activity':
            echo json_encode(logSuspiciousActivity($input));
            break;

        case 'sync_advanced_fingerprint':
            echo json_encode(syncAdvancedFingerprint($input));
            break;

        case 'analyze_advanced_risks':
            echo json_encode(analyzeAdvancedRisks($input));
            break;

        case 'get_device_risk_profile':
            echo json_encode(getDeviceRiskProfile($input));
            break;

        case 'get_device_fingerprints':
            echo json_encode(getDeviceFingerprints($input));
            break;

        case 'export_device_fingerprints':
            echo json_encode(exportDeviceFingerprints($input));
            break;

        default:
            throw new Exception('Неизвестное действие: ' . $action);
    }
    
} catch (Exception $e) {
    error_log('fraud-detection ERROR: ' . $e->getMessage());
    error_log('fraud-detection ERROR Stack: ' . $e->getTraceAsString());

    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'debug_info' => [
            'file' => basename($e->getFile()),
            'line' => $e->getLine(),
            'action' => $action ?? 'unknown'
        ]
    ]);
}

/**
 * Регистрирует отпечаток устройства пользователя
 */
function registerDeviceFingerprint($input) {
    if (!isset($input['initData']) || !isset($input['fingerprint_data'])) {
        throw new Exception('Отсутствуют обязательные параметры');
    }

    // Валидируем initData с поддержкой тестового режима
    $initData = $input['initData'];

    // Проверяем тестовые данные
    if (strpos($initData, 'test_fallback_mode') === 0) {
        // Тестовый режим - создаем фиктивные данные пользователя
        $userData = [
            'id' => 12345,
            'first_name' => 'Test',
            'last_name' => 'User',
            'username' => 'testuser'
        ];
        error_log("DEBUG: Используем тестовые данные пользователя для fallback режима");
    } else {
        // Обычная валидация
        $userData = validateInitData($initData);
        if (!$userData) {
            throw new Exception('Неверные данные авторизации');
        }
    }

    $userId = $userData['id'];
    $fingerprintData = $input['fingerprint_data'];
    $vpnData = $input['vpn_data'] ?? null;
    
    error_log("fraud-detection INFO: Регистрация отпечатка для пользователя {$userId}");
    
    // Загружаем данные пользователей
    $allUserData = loadUserData();

    // Если пользователь не существует - создаем базовую запись
    if (!isset($allUserData[$userId])) {
        error_log("fraud-detection INFO: Пользователь {$userId} не найден, создаем базовую запись");
        $allUserData[$userId] = [
            'id' => $userId,
            'balance' => 0,
            'total_earned' => 0,
            'joined' => time(),
            'referrer_id' => null,
            'referrals' => [],
            'referrals_count' => 0,
            'referral_earnings' => 0,
            'withdrawals' => [],
            'withdrawal_log' => [],
            'withdrawals_count' => 0,
            'username' => $userData['username'] ?? '',
            'first_name' => $userData['first_name'] ?? '',
            'last_name' => $userData['last_name'] ?? '',
            'language' => $userData['language_code'] ?? 'en',
            'registered_at' => time(),
            'last_activity' => time(),
            'suspicious_activity' => 0,
            'suspicious_activity_count' => 0,
            'blocked' => false
        ];
    }
    
    // Анализируем отпечаток на дубликаты и VPN
    $fraudAnalysis = analyzeFingerprintForFraud($userId, $fingerprintData, $allUserData, $vpnData);
    
    // Сохраняем отпечаток
    $allUserData[$userId]['device_fingerprint'] = $fingerprintData['fingerprint'];
    $allUserData[$userId]['fingerprint_components'] = $fingerprintData['components'];
    $allUserData[$userId]['fingerprint_registered_at'] = time();
    $allUserData[$userId]['fingerprint_ip'] = getRealIpAddr();
    
    // Если обнаружен фрод, помечаем пользователя
    if ($fraudAnalysis['is_fraud']) {
        $allUserData[$userId]['fraud_detected'] = true;
        $allUserData[$userId]['fraud_reason'] = $fraudAnalysis['reason'];
        $allUserData[$userId]['fraud_detected_at'] = time();
        
        // Если критический уровень фрода - блокируем (кроме админов)
        if ($fraudAnalysis['risk_level'] === 'CRITICAL' && !isAdminUser($userId)) {
            $allUserData[$userId]['blocked'] = true;
            $allUserData[$userId]['blocked_at'] = time();
            $allUserData[$userId]['block_reason'] = 'fraud_detection_' . $fraudAnalysis['reason'];
            
            error_log("fraud-detection ALERT: Пользователь {$userId} заблокирован за фрод: " . $fraudAnalysis['reason']);
        }
    }
    
    saveUserData($allUserData);

    // Сохраняем отпечаток в отдельный файл для админки
    saveAdvancedFingerprint($userId, $fingerprintData, $fraudAnalysis);

    return [
        'success' => true,
        'fraud_analysis' => $fraudAnalysis,
        'user_status' => [
            'blocked' => $allUserData[$userId]['blocked'] ?? false,
            'fraud_detected' => $allUserData[$userId]['fraud_detected'] ?? false
        ]
    ];
}

/**
 * Анализирует отпечаток устройства на предмет фрода
 */
function analyzeFingerprintForFraud($userId, $fingerprintData, $allUserData, $vpnData = null) {
    $fingerprint = $fingerprintData['fingerprint'];
    $components = $fingerprintData['components'];

    $fraudIndicators = [];
    $riskScore = 0;
    
    // 1. Проверяем дубликаты отпечатков
    $duplicateUsers = [];
    foreach ($allUserData as $existingUserId => $existingUser) {
        if ($existingUserId == $userId) continue;
        
        if (isset($existingUser['device_fingerprint']) && 
            $existingUser['device_fingerprint'] === $fingerprint) {
            $duplicateUsers[] = $existingUserId;
        }
    }
    
    if (!empty($duplicateUsers)) {
        $fraudIndicators[] = 'duplicate_fingerprint';
        $riskScore += 40;
        error_log("fraud-detection WARNING: Дубликат отпечатка для пользователя {$userId}, совпадает с: " . implode(', ', $duplicateUsers));
    }
    
    // 2. Проверяем подозрительные компоненты
    if (isset($components['canvas']) && $components['canvas'] === 'canvas_unavailable') {
        $fraudIndicators[] = 'canvas_disabled';
        $riskScore += 15;
    }
    
    if (isset($components['webgl']) && $components['webgl'] === 'webgl_unavailable') {
        $fraudIndicators[] = 'webgl_disabled';
        $riskScore += 10;
    }
    
    // 3. Проверяем подозрительные User Agent
    if (isset($components['system']['userAgent'])) {
        $userAgent = $components['system']['userAgent'];
        
        $suspiciousPatterns = [
            'headless', 'phantom', 'selenium', 'webdriver', 
            'bot', 'crawler', 'spider', 'automation'
        ];
        
        foreach ($suspiciousPatterns as $pattern) {
            if (stripos($userAgent, $pattern) !== false) {
                $fraudIndicators[] = 'suspicious_user_agent';
                $riskScore += 25;
                break;
            }
        }
    }
    
    // 4. Проверяем подозрительные разрешения экрана
    if (isset($components['screen'])) {
        $screen = $components['screen'];
        
        // Очень маленькие или очень большие разрешения подозрительны
        if ($screen['width'] < 800 || $screen['height'] < 600 || 
            $screen['width'] > 4000 || $screen['height'] > 3000) {
            $fraudIndicators[] = 'suspicious_screen_resolution';
            $riskScore += 5;
        }
    }
    
    // 5. Проверяем отсутствие плагинов (признак headless браузера)
    if (isset($components['navigator']['plugins']) &&
        empty($components['navigator']['plugins'])) {
        $fraudIndicators[] = 'no_plugins';
        $riskScore += 10;
    }

    // 6. Анализируем VPN/Proxy данные
    if ($vpnData) {
        if ($vpnData['is_vpn_detected']) {
            $fraudIndicators[] = 'vpn_detected';
            $riskScore += 30; // Высокий риск для VPN
        }

        // Добавляем баллы за клиентский риск-скор VPN
        $clientVpnRisk = $vpnData['risk_score'] ?? 0;
        if ($clientVpnRisk > 0) {
            $riskScore += min($clientVpnRisk / 2, 25); // Максимум 25 баллов от клиентского анализа
        }

        // Проверяем конкретные индикаторы VPN
        $vpnIndicators = $vpnData['indicators'] ?? [];
        foreach ($vpnIndicators as $indicator) {
            $fraudIndicators[] = 'vpn_' . $indicator;
            $riskScore += 5;
        }

        error_log("fraud-detection VPN: Пользователь {$userId}, VPN обнаружен: " .
                 ($vpnData['is_vpn_detected'] ? 'ДА' : 'НЕТ') .
                 ", риск-скор: {$clientVpnRisk}");
    }

    // Определяем уровень риска
    $riskLevel = 'LOW';
    if ($riskScore >= 50) $riskLevel = 'CRITICAL';
    elseif ($riskScore >= 30) $riskLevel = 'HIGH';
    elseif ($riskScore >= 15) $riskLevel = 'MEDIUM';
    
    return [
        'is_fraud' => $riskScore > 0,
        'risk_score' => $riskScore,
        'risk_level' => $riskLevel,
        'reason' => implode(', ', $fraudIndicators),
        'duplicate_users' => $duplicateUsers ?? [],
        'indicators' => $fraudIndicators
    ];
}

/**
 * Проверяет текущий статус фрода для пользователя
 */
function checkFraudScore($input) {
    if (!isset($input['initData'])) {
        throw new Exception('Отсутствует initData');
    }
    
    // Валидируем initData
    $userData = validateInitData($input['initData']);
    if (!$userData) {
        throw new Exception('Неверные данные авторизации');
    }
    
    $userId = $userData['id'];
    
    // Загружаем данные пользователя
    $allUserData = loadUserData();
    
    if (!isset($allUserData[$userId])) {
        throw new Exception('Пользователь не найден');
    }
    
    $user = $allUserData[$userId];
    
    return [
        'success' => true,
        'user_id' => $userId,
        'fraud_status' => [
            'blocked' => $user['blocked'] ?? false,
            'fraud_detected' => $user['fraud_detected'] ?? false,
            'fraud_reason' => $user['fraud_reason'] ?? null,
            'block_reason' => $user['block_reason'] ?? null,
            'has_fingerprint' => isset($user['device_fingerprint']),
            'fingerprint_registered_at' => $user['fingerprint_registered_at'] ?? null
        ]
    ];
}

/**
 * Получает статус фрода (для админки)
 */
function getFraudStatus($input) {
    // Проверяем права администратора (упрощенная проверка)
    if (!isset($input['admin_token']) || $input['admin_token'] !== 'admin_access') {
        throw new Exception('Недостаточно прав');
    }
    
    $allUserData = loadUserData();
    
    $stats = [
        'total_users' => count($allUserData),
        'users_with_fingerprints' => 0,
        'fraud_detected' => 0,
        'blocked_for_fraud' => 0,
        'duplicate_fingerprints' => 0
    ];
    
    $fingerprints = [];
    
    foreach ($allUserData as $userId => $user) {
        if (isset($user['device_fingerprint'])) {
            $stats['users_with_fingerprints']++;
            
            $fp = $user['device_fingerprint'];
            if (isset($fingerprints[$fp])) {
                $fingerprints[$fp]++;
            } else {
                $fingerprints[$fp] = 1;
            }
        }
        
        if ($user['fraud_detected'] ?? false) {
            $stats['fraud_detected']++;
        }
        
        if (($user['blocked'] ?? false) && 
            isset($user['block_reason']) && 
            strpos($user['block_reason'], 'fraud_detection') !== false) {
            $stats['blocked_for_fraud']++;
        }
    }
    
    // Подсчитываем дубликаты
    foreach ($fingerprints as $count) {
        if ($count > 1) {
            $stats['duplicate_fingerprints'] += $count;
        }
    }
    
    return [
        'success' => true,
        'stats' => $stats,
        'fingerprint_distribution' => array_count_values($fingerprints)
    ];
}

/**
 * Получает реальный IP адрес пользователя
 */
function getRealIpAddr() {
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        return $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        return $_SERVER['HTTP_X_FORWARDED_FOR'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED'])) {
        return $_SERVER['HTTP_X_FORWARDED'];
    } elseif (!empty($_SERVER['HTTP_FORWARDED_FOR'])) {
        return $_SERVER['HTTP_FORWARDED_FOR'];
    } elseif (!empty($_SERVER['HTTP_FORWARDED'])) {
        return $_SERVER['HTTP_FORWARDED'];
    } elseif (!empty($_SERVER['REMOTE_ADDR'])) {
        return $_SERVER['REMOTE_ADDR'];
    }
    return 'unknown';
}

// ===== ФУНКЦИИ ДЛЯ АДМИНКИ =====

/**
 * Получает статистику антифрод системы для админки
 */
function getAdminStats() {
    try {
        $fingerprintsFile = __DIR__ . '/../database/advanced_fingerprints.json';
        $fraudLogFile = __DIR__ . '/../database/fraud_log.json';
        $blockedDevicesFile = __DIR__ . '/../database/blocked_devices.json';
        $vpnLogFile = __DIR__ . '/../database/vpn_analysis_log.json';

        // Загружаем данные
        $fingerprints = [];
        if (file_exists($fingerprintsFile)) {
            $fingerprints = json_decode(file_get_contents($fingerprintsFile), true) ?: [];
        }

        $fraudLog = [];
        if (file_exists($fraudLogFile)) {
            $fraudLog = json_decode(file_get_contents($fraudLogFile), true) ?: [];
        }

        $blockedDevices = [];
        if (file_exists($blockedDevicesFile)) {
            $blockedDevices = json_decode(file_get_contents($blockedDevicesFile), true) ?: [];
        }

        $vpnLog = [];
        if (file_exists($vpnLogFile)) {
            $vpnLog = json_decode(file_get_contents($vpnLogFile), true) ?: [];
        }

        // Подсчитываем статистику
        $stats = [
            'total_fingerprints' => count($fingerprints),
            'blocked_devices' => count($blockedDevices),
            'duplicate_fingerprints' => 0,
            'fraud_attempts' => count($fraudLog),
            'vpn_detections' => count($vpnLog),
            'vpn_blocked' => count(array_filter($vpnLog, function($entry) {
                return $entry['vpn_detected'] ?? false;
            }))
        ];

        // Подсчитываем дубликаты отпечатков
        $fingerprintCounts = [];
        foreach ($fingerprints as $entry) {
            $fingerprint = $entry['fingerprint'] ?? '';
            if ($fingerprint) {
                $fingerprintCounts[$fingerprint] = ($fingerprintCounts[$fingerprint] ?? 0) + 1;
            }
        }

        foreach ($fingerprintCounts as $count) {
            if ($count > 1) {
                $stats['duplicate_fingerprints']++;
            }
        }

        // Получаем последние 50 записей журнала фрода
        $recentFraudLog = array_slice(array_reverse($fraudLog), 0, 50);

        // Получаем информацию о заблокированных устройствах
        $blockedDevicesInfo = [];
        foreach ($blockedDevices as $fingerprint => $deviceInfo) {
            $userCount = 0;
            foreach ($fingerprints as $entry) {
                if (($entry['fingerprint'] ?? '') === $fingerprint) {
                    $userCount++;
                }
            }

            $blockedDevicesInfo[] = [
                'fingerprint' => $fingerprint,
                'user_count' => $userCount,
                'blocked_at' => $deviceInfo['blocked_at'] ?? time(),
                'reason' => $deviceInfo['reason'] ?? 'Неизвестно'
            ];
        }

        return [
            'success' => true,
            'stats' => $stats,
            'fraud_log' => $recentFraudLog,
            'blocked_devices' => $blockedDevicesInfo
        ];

    } catch (Exception $e) {
        error_log('getAdminStats ERROR: ' . $e->getMessage());
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Получает текущие настройки антифрод системы
 */
function getAdminSettings() {
    try {
        $settingsFile = __DIR__ . '/../database/antifraud_settings.json';

        $defaultSettings = [
            'enable_antifraud' => true,
            'fraud_threshold' => 50,
            'block_vpn' => false,
            'block_duplicate_fingerprints' => true,
            'block_self_referrals' => true,
            'updated_at' => time()
        ];

        if (file_exists($settingsFile)) {
            $settings = json_decode(file_get_contents($settingsFile), true);
            if ($settings) {
                // Объединяем с настройками по умолчанию для обратной совместимости
                $settings = array_merge($defaultSettings, $settings);
            } else {
                $settings = $defaultSettings;
            }
        } else {
            $settings = $defaultSettings;
        }

        return [
            'success' => true,
            'settings' => $settings
        ];

    } catch (Exception $e) {
        error_log('getAdminSettings ERROR: ' . $e->getMessage());
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Сохраняет настройки антифрод системы
 */
function saveAdminSettings($input) {
    try {
        if (!isset($input['settings'])) {
            throw new Exception('Отсутствуют настройки для сохранения');
        }

        $settings = $input['settings'];
        $settingsFile = __DIR__ . '/../database/antifraud_settings.json';

        // Валидация настроек
        $validatedSettings = [
            'enable_antifraud' => (bool)($settings['enable_antifraud'] ?? true),
            'fraud_threshold' => max(10, min(100, (int)($settings['fraud_threshold'] ?? 50))),
            'block_vpn' => (bool)($settings['block_vpn'] ?? false),
            'block_duplicate_fingerprints' => (bool)($settings['block_duplicate_fingerprints'] ?? true),
            'block_self_referrals' => (bool)($settings['block_self_referrals'] ?? true),
            'updated_at' => time()
        ];

        // Сохраняем настройки
        if (!file_put_contents($settingsFile, json_encode($validatedSettings, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE))) {
            throw new Exception('Не удалось сохранить настройки');
        }

        return [
            'success' => true,
            'message' => 'Настройки антифрод системы сохранены'
        ];

    } catch (Exception $e) {
        error_log('saveAdminSettings ERROR: ' . $e->getMessage());
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Очищает журнал фрода
 */
function clearFraudLog() {
    try {
        $fraudLogFile = __DIR__ . '/../database/fraud_log.json';

        if (file_exists($fraudLogFile)) {
            if (!file_put_contents($fraudLogFile, json_encode([], JSON_PRETTY_PRINT))) {
                throw new Exception('Не удалось очистить журнал фрода');
            }
        }

        return [
            'success' => true,
            'message' => 'Журнал фрода очищен'
        ];

    } catch (Exception $e) {
        error_log('clearFraudLog ERROR: ' . $e->getMessage());
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Разблокирует устройство
 */
function unblockDevice($input) {
    try {
        if (!isset($input['fingerprint'])) {
            throw new Exception('Отсутствует отпечаток устройства для разблокировки');
        }

        $fingerprint = $input['fingerprint'];
        $blockedDevicesFile = __DIR__ . '/../database/blocked_devices.json';

        $blockedDevices = [];
        if (file_exists($blockedDevicesFile)) {
            $blockedDevices = json_decode(file_get_contents($blockedDevicesFile), true) ?: [];
        }

        if (isset($blockedDevices[$fingerprint])) {
            unset($blockedDevices[$fingerprint]);

            if (!file_put_contents($blockedDevicesFile, json_encode($blockedDevices, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE))) {
                throw new Exception('Не удалось сохранить изменения');
            }
        }

        return [
            'success' => true,
            'message' => 'Устройство разблокировано'
        ];

    } catch (Exception $e) {
        error_log('unblockDevice ERROR: ' . $e->getMessage());
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Логирует подозрительную активность
 */
function logSuspiciousActivity($input) {
    try {
        $fraudLogFile = __DIR__ . '/../database/fraud_log.json';

        $fraudLog = [];
        if (file_exists($fraudLogFile)) {
            $fraudLog = json_decode(file_get_contents($fraudLogFile), true) ?: [];
        }

        $logEntry = [
            'timestamp' => time(),
            'user_id' => extractUserIdFromInitData($input['initData'] ?? ''),
            'violation_type' => $input['activity_type'] ?? 'unknown',
            'risk_level' => 'MEDIUM',
            'risk_score' => 25,
            'action' => 'logged',
            'details' => json_encode($input['details'] ?? []),
            'fingerprint' => $input['fingerprint'] ?? null,
            'ip_address' => getRealIpAddr()
        ];

        $fraudLog[] = $logEntry;

        // Ограничиваем размер лога (последние 1000 записей)
        if (count($fraudLog) > 1000) {
            $fraudLog = array_slice($fraudLog, -1000);
        }

        if (!file_put_contents($fraudLogFile, json_encode($fraudLog, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE))) {
            throw new Exception('Не удалось сохранить запись в журнал фрода');
        }

        return [
            'success' => true,
            'message' => 'Подозрительная активность зарегистрирована'
        ];

    } catch (Exception $e) {
        error_log('logSuspiciousActivity ERROR: ' . $e->getMessage());
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Анализирует реферральные связи на предмет самореферралов
 */
function analyzeReferrals($input) {
    try {
        $user_id = $input['user_id'] ?? null;
        $fingerprint = $input['fingerprint'] ?? null;
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'unknown';

        if (!$user_id) {
            throw new Exception('user_id is required');
        }

        $violations = [];
        $risk_score = 0;

        // 1. Проверяем циклические рефералы
        $cyclicCheck = checkCyclicReferrals($user_id);
        if ($cyclicCheck['found']) {
            $violations[] = [
                'type' => 'cyclic_referral',
                'description' => 'Обнаружена циклическая реферральная цепочка',
                'details' => $cyclicCheck['chain'],
                'risk_points' => 30
            ];
            $risk_score += 30;
        }

        // 2. Проверяем дублирование отпечатков
        if ($fingerprint) {
            $fingerprintCheck = checkFingerprintDuplicates(['fingerprint' => $fingerprint, 'user_id' => $user_id]);
            if ($fingerprintCheck['duplicates_found']) {
                $violations[] = [
                    'type' => 'fingerprint_duplicate',
                    'description' => 'Обнаружены дублирующиеся отпечатки устройств',
                    'details' => $fingerprintCheck['duplicate_users'],
                    'risk_points' => 25
                ];
                $risk_score += 25;
            }
        }

        // 3. Проверяем IP паттерны
        $ipCheck = checkIPPatterns($user_id, $ip_address);
        if ($ipCheck['suspicious']) {
            $violations[] = [
                'type' => 'suspicious_ip_pattern',
                'description' => 'Подозрительные IP паттерны в реферральной сети',
                'details' => $ipCheck['details'],
                'risk_points' => 20
            ];
            $risk_score += 20;
        }

        // 4. Проверяем временные паттерны
        $timingCheck = checkTimingPatterns($user_id);
        if ($timingCheck['suspicious']) {
            $violations[] = [
                'type' => 'suspicious_timing',
                'description' => 'Подозрительные временные паттерны регистрации',
                'details' => $timingCheck['details'],
                'risk_points' => 15
            ];
            $risk_score += 15;
        }

        // Определяем, нужно ли блокировать
        $should_block = $risk_score >= 50; // Блокируем при 50+ баллах

        // Логируем результат
        logFraudAnalysis($user_id, $ip_address, $fingerprint, $violations, $risk_score, $should_block);

        return [
            'success' => true,
            'should_block' => $should_block,
            'risk_score' => $risk_score,
            'violations' => $violations,
            'recommendations' => generateRecommendations($violations)
        ];

    } catch (Exception $e) {
        error_log('analyzeReferrals ERROR: ' . $e->getMessage());
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Проверяет циклические рефералы
 */
function checkCyclicReferrals($user_id) {
    $users_file = __DIR__ . '/../database/users.json';

    if (!file_exists($users_file)) {
        return ['found' => false, 'chain' => []];
    }

    $users = json_decode(file_get_contents($users_file), true) ?: [];

    // Строим граф реферральных связей
    $referral_graph = [];
    foreach ($users as $user) {
        if (isset($user['referrer_id']) && $user['referrer_id']) {
            $referral_graph[$user['id']] = $user['referrer_id'];
        }
    }

    // Проверяем циклы, начиная с текущего пользователя
    $visited = [];
    $current = $user_id;
    $chain = [$current];

    while (isset($referral_graph[$current])) {
        $next = $referral_graph[$current];

        if (in_array($next, $visited)) {
            // Найден цикл
            $cycle_start = array_search($next, $chain);
            $cycle = array_slice($chain, $cycle_start);
            $cycle[] = $next; // Замыкаем цикл

            return [
                'found' => true,
                'chain' => $cycle
            ];
        }

        $visited[] = $current;
        $chain[] = $next;
        $current = $next;

        // Защита от бесконечного цикла
        if (count($chain) > 50) {
            break;
        }
    }

    return ['found' => false, 'chain' => []];
}

/**
 * Проверяет дублирование отпечатков устройств
 */
function checkFingerprintDuplicates($input) {
    $fingerprint = $input['fingerprint'] ?? null;
    $current_user_id = $input['user_id'] ?? null;

    if (!$fingerprint) {
        return ['duplicates_found' => false, 'duplicate_users' => []];
    }

    $fingerprints_file = __DIR__ . '/../database/fingerprints.json';

    if (!file_exists($fingerprints_file)) {
        return ['duplicates_found' => false, 'duplicate_users' => []];
    }

    $fingerprints = json_decode(file_get_contents($fingerprints_file), true) ?: [];

    $duplicate_users = [];

    foreach ($fingerprints as $record) {
        if ($record['fingerprint'] === $fingerprint && $record['user_id'] != $current_user_id) {
            $duplicate_users[] = [
                'user_id' => $record['user_id'],
                'created_at' => $record['created_at'] ?? 'unknown',
                'ip_address' => $record['ip_address'] ?? 'unknown'
            ];
        }
    }

    return [
        'duplicates_found' => count($duplicate_users) > 0,
        'duplicate_users' => $duplicate_users
    ];
}

/**
 * Проверяет подозрительные IP паттерны
 */
function checkIPPatterns($user_id, $ip_address) {
    $users_file = __DIR__ . '/../database/users.json';

    if (!file_exists($users_file)) {
        return ['suspicious' => false, 'details' => []];
    }

    $users = json_decode(file_get_contents($users_file), true) ?: [];

    // Ищем пользователей с тем же IP
    $same_ip_users = [];
    foreach ($users as $user) {
        if (isset($user['ip_address']) && $user['ip_address'] === $ip_address && $user['id'] != $user_id) {
            $same_ip_users[] = $user['id'];
        }
    }

    // Подозрительно, если больше 3 пользователей с одного IP
    $suspicious = count($same_ip_users) > 3;

    return [
        'suspicious' => $suspicious,
        'details' => [
            'same_ip_count' => count($same_ip_users),
            'same_ip_users' => $same_ip_users
        ]
    ];
}

/**
 * Проверяет подозрительные временные паттерны
 */
function checkTimingPatterns($user_id) {
    $users_file = __DIR__ . '/../database/users.json';

    if (!file_exists($users_file)) {
        return ['suspicious' => false, 'details' => []];
    }

    $users = json_decode(file_get_contents($users_file), true) ?: [];

    $current_user = null;
    foreach ($users as $user) {
        if ($user['id'] == $user_id) {
            $current_user = $user;
            break;
        }
    }

    if (!$current_user || !isset($current_user['created_at'])) {
        return ['suspicious' => false, 'details' => []];
    }

    $user_time = strtotime($current_user['created_at']);
    $recent_registrations = 0;

    // Считаем регистрации в течение последнего часа
    foreach ($users as $user) {
        if (isset($user['created_at'])) {
            $reg_time = strtotime($user['created_at']);
            if (abs($user_time - $reg_time) <= 3600) { // 1 час
                $recent_registrations++;
            }
        }
    }

    // Подозрительно, если больше 5 регистраций за час
    $suspicious = $recent_registrations > 5;

    return [
        'suspicious' => $suspicious,
        'details' => [
            'recent_registrations' => $recent_registrations,
            'time_window' => '1 hour'
        ]
    ];
}

/**
 * Получает реферральную цепочку пользователя
 */
function getReferralChain($input) {
    $user_id = $input['user_id'] ?? null;

    if (!$user_id) {
        throw new Exception('user_id is required');
    }

    $users_file = __DIR__ . '/../database/users.json';

    if (!file_exists($users_file)) {
        return ['chain' => []];
    }

    $users = json_decode(file_get_contents($users_file), true) ?: [];

    // Строим цепочку вверх (к корневому рефереру)
    $chain = [];
    $current = $user_id;

    while ($current) {
        $user = null;
        foreach ($users as $u) {
            if ($u['id'] == $current) {
                $user = $u;
                break;
            }
        }

        if (!$user) break;

        $chain[] = [
            'user_id' => $user['id'],
            'username' => $user['username'] ?? 'unknown',
            'created_at' => $user['created_at'] ?? 'unknown'
        ];

        $current = $user['referrer_id'] ?? null;

        // Защита от бесконечного цикла
        if (count($chain) > 20) break;
    }

    return ['chain' => $chain];
}

/**
 * Генерирует рекомендации на основе нарушений
 */
function generateRecommendations($violations) {
    $recommendations = [];

    foreach ($violations as $violation) {
        switch ($violation['type']) {
            case 'cyclic_referral':
                $recommendations[] = 'Заблокировать всех пользователей в циклической цепочке';
                break;
            case 'fingerprint_duplicate':
                $recommendations[] = 'Проверить устройства с дублирующимися отпечатками';
                break;
            case 'suspicious_ip_pattern':
                $recommendations[] = 'Ограничить регистрации с данного IP';
                break;
            case 'suspicious_timing':
                $recommendations[] = 'Усилить проверки для массовых регистраций';
                break;
        }
    }

    return $recommendations;
}

/**
 * Логирует результаты анализа фрода
 */
function logFraudAnalysis($user_id, $ip_address, $fingerprint, $violations, $risk_score, $should_block) {
    $log_file = __DIR__ . '/../database/fraud_analysis.json';

    $log_entry = [
        'timestamp' => date('Y-m-d H:i:s'),
        'user_id' => $user_id,
        'ip_address' => $ip_address,
        'fingerprint' => $fingerprint,
        'violations_count' => count($violations),
        'risk_score' => $risk_score,
        'should_block' => $should_block,
        'violations' => $violations
    ];

    $logs = [];
    if (file_exists($log_file)) {
        $logs = json_decode(file_get_contents($log_file), true) ?: [];
    }

    $logs[] = $log_entry;

    // Оставляем только последние 1000 записей
    if (count($logs) > 1000) {
        $logs = array_slice($logs, -1000);
    }

    file_put_contents($log_file, json_encode($logs, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
}

/**
 * Синхронизирует расширенные данные fingerprinting с сервером
 */
function syncAdvancedFingerprint($input) {
    try {
        if (!isset($input['fingerprint']) || !isset($input['components']) || !isset($input['riskScore'])) {
            throw new Exception('Отсутствуют обязательные параметры для синхронизации');
        }

        $fingerprint = $input['fingerprint'];
        $components = $input['components'];
        $risk_score = $input['riskScore'];
        $risk_assessment = $input['riskAssessment'] ?? [];
        $init_data = $input['initData'] ?? '';

        // Извлекаем user_id из initData
        $user_id = extractUserIdFromInitData($init_data);
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'unknown';

        // Сохраняем расширенные данные fingerprint
        $advanced_fingerprint_data = [
            'fingerprint' => $fingerprint,
            'components' => $components,
            'risk_score' => $risk_score,
            'risk_assessment' => $risk_assessment,
            'user_id' => $user_id,
            'ip_address' => $ip_address,
            'timestamp' => date('Y-m-d H:i:s'),
            'created_at' => time()
        ];

        // Сохраняем в файл расширенных fingerprints
        $advanced_fingerprints_file = __DIR__ . '/../database/advanced_fingerprints.json';
        $advanced_fingerprints = [];

        if (file_exists($advanced_fingerprints_file)) {
            $advanced_fingerprints = json_decode(file_get_contents($advanced_fingerprints_file), true) ?: [];
        }

        $advanced_fingerprints[$fingerprint] = $advanced_fingerprint_data;

        // Ограничиваем количество записей (последние 5000)
        if (count($advanced_fingerprints) > 5000) {
            $sorted = $advanced_fingerprints;
            uasort($sorted, function($a, $b) {
                return $b['created_at'] - $a['created_at'];
            });
            $advanced_fingerprints = array_slice($sorted, 0, 5000, true);
        }

        file_put_contents($advanced_fingerprints_file, json_encode($advanced_fingerprints, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

        // DEBUG: Логируем сохранение отпечатка
        error_log("DEBUG: Отпечаток сохранен в файл. Fingerprint: $fingerprint, User ID: $user_id, Total fingerprints: " . count($advanced_fingerprints));

        // Выполняем дополнительный анализ рисков
        $additional_analysis = performAdvancedRiskAnalysis($advanced_fingerprint_data);

        // Логируем если высокий риск
        if ($risk_score >= 70) {
            logAdvancedFraudActivity($user_id, $ip_address, $fingerprint, $risk_assessment, $risk_score);
        }

        return [
            'success' => true,
            'fingerprint' => $fingerprint,
            'risk_score' => $risk_score,
            'additional_analysis' => $additional_analysis,
            'timestamp' => time()
        ];

    } catch (Exception $e) {
        error_log('syncAdvancedFingerprint ERROR: ' . $e->getMessage());
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Анализирует расширенные риски на основе fingerprint данных
 */
function analyzeAdvancedRisks($input) {
    try {
        if (!isset($input['fingerprint'])) {
            throw new Exception('Отсутствует fingerprint для анализа');
        }

        $fingerprint = $input['fingerprint'];

        // Загружаем данные расширенных fingerprints
        $advanced_fingerprints_file = __DIR__ . '/../database/advanced_fingerprints.json';
        $advanced_fingerprints = [];

        if (file_exists($advanced_fingerprints_file)) {
            $advanced_fingerprints = json_decode(file_get_contents($advanced_fingerprints_file), true) ?: [];
        }

        if (!isset($advanced_fingerprints[$fingerprint])) {
            throw new Exception('Fingerprint не найден в базе данных');
        }

        $fingerprint_data = $advanced_fingerprints[$fingerprint];

        // Выполняем расширенный анализ
        $analysis = [
            'fingerprint' => $fingerprint,
            'base_risk_score' => $fingerprint_data['risk_score'],
            'risk_factors' => [],
            'recommendations' => [],
            'historical_analysis' => [],
            'device_reputation' => 'unknown'
        ];

        // Анализ компонентов fingerprint
        $components = $fingerprint_data['components'];

        // 1. Анализ детекции эмуляторов
        if (isset($components['emulatorDetection']) && $components['emulatorDetection']['isEmulator']) {
            $analysis['risk_factors'][] = [
                'type' => 'emulator_detected',
                'severity' => 'high',
                'score' => $components['emulatorDetection']['riskPoints'],
                'indicators' => $components['emulatorDetection']['indicators']
            ];
        }

        // 2. Анализ детекции ботов
        if (isset($components['botDetection']) && $components['botDetection']['isBot']) {
            $analysis['risk_factors'][] = [
                'type' => 'bot_detected',
                'severity' => 'high',
                'score' => $components['botDetection']['riskPoints'],
                'indicators' => $components['botDetection']['indicators']
            ];
        }

        // 3. Анализ spoofing
        if (isset($components['spoofingDetection']) && $components['spoofingDetection']['isSpoofed']) {
            $analysis['risk_factors'][] = [
                'type' => 'spoofing_detected',
                'severity' => 'critical',
                'score' => $components['spoofingDetection']['riskPoints'],
                'indicators' => $components['spoofingDetection']['indicators']
            ];
        }

        // 4. Анализ производительности (подозрительно высокая может указывать на бота)
        if (isset($components['performance']) && is_array($components['performance'])) {
            $js_perf = $components['performance']['jsPerformance'] ?? 0;
            if ($js_perf > 10000) {
                $analysis['risk_factors'][] = [
                    'type' => 'suspicious_performance',
                    'severity' => 'medium',
                    'score' => 15,
                    'details' => ['js_performance' => $js_perf]
                ];
            }
        }

        // 5. Анализ поведенческих данных
        if (isset($components['behavioral'])) {
            $behavioral = $components['behavioral'];

            // Отсутствие активности мыши
            if ($behavioral['mouseEventsCount'] == 0 && $behavioral['sessionDuration'] > 5000) {
                $analysis['risk_factors'][] = [
                    'type' => 'no_mouse_activity',
                    'severity' => 'high',
                    'score' => 20,
                    'details' => $behavioral
                ];
            }
        }

        // Вычисляем итоговый risk score
        $total_additional_risk = 0;
        foreach ($analysis['risk_factors'] as $factor) {
            $total_additional_risk += $factor['score'];
        }

        $analysis['total_risk_score'] = $analysis['base_risk_score'] + $total_additional_risk;

        // Определяем репутацию устройства
        if ($analysis['total_risk_score'] >= 85) {
            $analysis['device_reputation'] = 'malicious';
        } elseif ($analysis['total_risk_score'] >= 70) {
            $analysis['device_reputation'] = 'suspicious';
        } elseif ($analysis['total_risk_score'] >= 50) {
            $analysis['device_reputation'] = 'questionable';
        } else {
            $analysis['device_reputation'] = 'clean';
        }

        // Генерируем рекомендации
        $analysis['recommendations'] = generateAdvancedRecommendations($analysis);

        return [
            'success' => true,
            'analysis' => $analysis
        ];

    } catch (Exception $e) {
        error_log('analyzeAdvancedRisks ERROR: ' . $e->getMessage());
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Получает профиль рисков устройства
 */
function getDeviceRiskProfile($input) {
    try {
        if (!isset($input['fingerprint'])) {
            throw new Exception('Отсутствует fingerprint');
        }

        $fingerprint = $input['fingerprint'];

        // Загружаем данные
        $advanced_fingerprints_file = __DIR__ . '/../database/advanced_fingerprints.json';
        $fraud_log_file = __DIR__ . '/../database/fraud_log.json';

        $advanced_fingerprints = [];
        $fraud_logs = [];

        if (file_exists($advanced_fingerprints_file)) {
            $advanced_fingerprints = json_decode(file_get_contents($advanced_fingerprints_file), true) ?: [];
        }

        if (file_exists($fraud_log_file)) {
            $fraud_logs = json_decode(file_get_contents($fraud_log_file), true) ?: [];
        }

        if (!isset($advanced_fingerprints[$fingerprint])) {
            throw new Exception('Устройство не найдено');
        }

        $device_data = $advanced_fingerprints[$fingerprint];

        // Собираем профиль рисков
        $profile = [
            'fingerprint' => $fingerprint,
            'first_seen' => $device_data['timestamp'],
            'last_seen' => $device_data['timestamp'],
            'risk_score' => $device_data['risk_score'],
            'user_id' => $device_data['user_id'],
            'ip_address' => $device_data['ip_address'],
            'device_info' => extractDeviceInfo($device_data['components']),
            'risk_factors' => $device_data['risk_assessment']['riskFactors'] ?? [],
            'fraud_history' => [],
            'reputation' => 'unknown'
        ];

        // Ищем историю фрода для этого устройства
        foreach ($fraud_logs as $log) {
            if ($log['fingerprint'] === $fingerprint) {
                $profile['fraud_history'][] = [
                    'timestamp' => $log['timestamp'],
                    'violations' => $log['violations'],
                    'risk_score' => $log['risk_score']
                ];
            }
        }

        // Определяем репутацию
        if ($profile['risk_score'] >= 85 || count($profile['fraud_history']) > 3) {
            $profile['reputation'] = 'malicious';
        } elseif ($profile['risk_score'] >= 70 || count($profile['fraud_history']) > 1) {
            $profile['reputation'] = 'suspicious';
        } elseif ($profile['risk_score'] >= 50) {
            $profile['reputation'] = 'questionable';
        } else {
            $profile['reputation'] = 'clean';
        }

        return [
            'success' => true,
            'profile' => $profile
        ];

    } catch (Exception $e) {
        error_log('getDeviceRiskProfile ERROR: ' . $e->getMessage());
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Выполняет дополнительный анализ рисков для расширенного fingerprint
 */
function performAdvancedRiskAnalysis($fingerprint_data) {
    $analysis = [
        'duplicate_fingerprints' => 0,
        'ip_reputation' => 'unknown',
        'device_consistency' => 'consistent',
        'behavioral_anomalies' => [],
        'additional_risk_score' => 0
    ];

    try {
        // 1. Проверяем дубликаты fingerprint
        $advanced_fingerprints_file = __DIR__ . '/../database/advanced_fingerprints.json';
        if (file_exists($advanced_fingerprints_file)) {
            $all_fingerprints = json_decode(file_get_contents($advanced_fingerprints_file), true) ?: [];

            $current_fingerprint = $fingerprint_data['fingerprint'];
            $duplicates = 0;

            foreach ($all_fingerprints as $fp => $data) {
                if ($fp !== $current_fingerprint &&
                    $data['user_id'] !== $fingerprint_data['user_id'] &&
                    $fp === $current_fingerprint) {
                    $duplicates++;
                }
            }

            $analysis['duplicate_fingerprints'] = $duplicates;
            if ($duplicates > 0) {
                $analysis['additional_risk_score'] += $duplicates * 15;
            }
        }

        // 2. Анализ IP репутации (базовая проверка)
        $ip = $fingerprint_data['ip_address'];
        if ($ip !== 'unknown') {
            // Проверяем частоту использования IP
            $ip_usage_count = 0;
            if (file_exists($advanced_fingerprints_file)) {
                $all_fingerprints = json_decode(file_get_contents($advanced_fingerprints_file), true) ?: [];

                foreach ($all_fingerprints as $data) {
                    if ($data['ip_address'] === $ip) {
                        $ip_usage_count++;
                    }
                }
            }

            if ($ip_usage_count > 10) {
                $analysis['ip_reputation'] = 'suspicious';
                $analysis['additional_risk_score'] += 20;
            } elseif ($ip_usage_count > 5) {
                $analysis['ip_reputation'] = 'questionable';
                $analysis['additional_risk_score'] += 10;
            } else {
                $analysis['ip_reputation'] = 'clean';
            }
        }

        // 3. Проверка консистентности устройства
        $components = $fingerprint_data['components'];

        // Проверяем логичность данных экрана
        if (isset($components['screen'])) {
            $screen = $components['screen'];

            if ($screen['width'] > $screen['availWidth'] ||
                $screen['height'] > $screen['availHeight']) {
                $analysis['device_consistency'] = 'inconsistent';
                $analysis['additional_risk_score'] += 15;
            }
        }

        // 4. Анализ поведенческих аномалий
        if (isset($components['behavioral'])) {
            $behavioral = $components['behavioral'];

            // Слишком быстрая активность
            if ($behavioral['sessionDuration'] < 1000 &&
                ($behavioral['mouseEventsCount'] > 50 || $behavioral['keyboardEventsCount'] > 20)) {
                $analysis['behavioral_anomalies'][] = 'too_fast_activity';
                $analysis['additional_risk_score'] += 15;
            }

            // Отсутствие естественной активности
            if ($behavioral['sessionDuration'] > 10000 &&
                $behavioral['mouseEventsCount'] === 0 &&
                $behavioral['keyboardEventsCount'] === 0) {
                $analysis['behavioral_anomalies'][] = 'no_natural_activity';
                $analysis['additional_risk_score'] += 20;
            }
        }

    } catch (Exception $e) {
        error_log('performAdvancedRiskAnalysis ERROR: ' . $e->getMessage());
        $analysis['error'] = $e->getMessage();
    }

    return $analysis;
}

/**
 * Генерирует рекомендации на основе расширенного анализа
 */
function generateAdvancedRecommendations($analysis) {
    $recommendations = [];

    $total_risk = $analysis['total_risk_score'];

    // Основные рекомендации по уровню риска
    if ($total_risk >= 90) {
        $recommendations[] = 'immediate_permanent_block';
        $recommendations[] = 'investigate_user_account';
        $recommendations[] = 'check_related_devices';
    } elseif ($total_risk >= 80) {
        $recommendations[] = 'temporary_block_24h';
        $recommendations[] = 'enhanced_monitoring';
        $recommendations[] = 'require_additional_verification';
    } elseif ($total_risk >= 70) {
        $recommendations[] = 'enhanced_monitoring';
        $recommendations[] = 'limit_sensitive_actions';
        $recommendations[] = 'periodic_reverification';
    } elseif ($total_risk >= 50) {
        $recommendations[] = 'basic_monitoring';
        $recommendations[] = 'log_all_actions';
    } else {
        $recommendations[] = 'normal_access';
    }

    // Специфичные рекомендации по типам рисков
    foreach ($analysis['risk_factors'] as $factor) {
        switch ($factor['type']) {
            case 'emulator_detected':
                $recommendations[] = 'block_emulator_access';
                break;
            case 'bot_detected':
                $recommendations[] = 'implement_captcha';
                $recommendations[] = 'rate_limit_actions';
                break;
            case 'spoofing_detected':
                $recommendations[] = 'investigate_spoofing_tools';
                $recommendations[] = 'enhanced_device_verification';
                break;
            case 'suspicious_performance':
                $recommendations[] = 'verify_human_behavior';
                break;
            case 'no_mouse_activity':
                $recommendations[] = 'require_mouse_interaction';
                break;
        }
    }

    return array_unique($recommendations);
}

/**
 * Логирует расширенную активность фрода
 */
function logAdvancedFraudActivity($user_id, $ip_address, $fingerprint, $risk_assessment, $risk_score) {
    try {
        $log_file = __DIR__ . '/../database/advanced_fraud_log.json';

        $log_entry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'user_id' => $user_id,
            'ip_address' => $ip_address,
            'fingerprint' => $fingerprint,
            'risk_score' => $risk_score,
            'risk_factors' => $risk_assessment['riskFactors'] ?? [],
            'blocking_reasons' => $risk_assessment['blockingReasons'] ?? [],
            'recommendations' => $risk_assessment['recommendations'] ?? [],
            'severity' => $risk_score >= 85 ? 'critical' : ($risk_score >= 70 ? 'high' : 'medium')
        ];

        $logs = [];
        if (file_exists($log_file)) {
            $logs = json_decode(file_get_contents($log_file), true) ?: [];
        }

        $logs[] = $log_entry;

        // Оставляем только последние 2000 записей
        if (count($logs) > 2000) {
            $logs = array_slice($logs, -2000);
        }

        file_put_contents($log_file, json_encode($logs, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

    } catch (Exception $e) {
        error_log('logAdvancedFraudActivity ERROR: ' . $e->getMessage());
    }
}

/**
 * Извлекает информацию об устройстве из компонентов fingerprint
 */
function extractDeviceInfo($components) {
    $device_info = [
        'platform' => 'unknown',
        'browser' => 'unknown',
        'screen_resolution' => 'unknown',
        'hardware_concurrency' => 'unknown',
        'device_memory' => 'unknown',
        'webgl_vendor' => 'unknown',
        'webgl_renderer' => 'unknown'
    ];

    try {
        // Извлекаем системную информацию
        if (isset($components['system'])) {
            $system = $components['system'];
            $device_info['platform'] = $system['platform'] ?? 'unknown';
            $device_info['hardware_concurrency'] = $system['hardwareConcurrency'] ?? 'unknown';
            $device_info['device_memory'] = $system['deviceMemory'] ?? 'unknown';

            // Парсим User-Agent для определения браузера
            if (isset($system['userAgent'])) {
                $ua = $system['userAgent'];
                if (strpos($ua, 'Chrome') !== false) {
                    $device_info['browser'] = 'Chrome';
                } elseif (strpos($ua, 'Firefox') !== false) {
                    $device_info['browser'] = 'Firefox';
                } elseif (strpos($ua, 'Safari') !== false) {
                    $device_info['browser'] = 'Safari';
                } elseif (strpos($ua, 'Edge') !== false) {
                    $device_info['browser'] = 'Edge';
                }
            }
        }

        // Извлекаем информацию об экране
        if (isset($components['screen'])) {
            $screen = $components['screen'];
            $device_info['screen_resolution'] = ($screen['width'] ?? 'unknown') . 'x' . ($screen['height'] ?? 'unknown');
        }

        // Извлекаем информацию о WebGL
        if (isset($components['webgl']) && is_array($components['webgl'])) {
            $webgl = $components['webgl'];
            $device_info['webgl_vendor'] = $webgl['vendor'] ?? 'unknown';
            $device_info['webgl_renderer'] = $webgl['renderer'] ?? 'unknown';
        }

    } catch (Exception $e) {
        error_log('extractDeviceInfo ERROR: ' . $e->getMessage());
    }

    return $device_info;
}

/**
 * Извлекает user_id из Telegram initData
 */
function extractUserIdFromInitData($initData) {
    try {
        if (empty($initData)) {
            // Fallback для тестирования
            return '12345';
        }

        // Парсим initData
        $params = [];
        parse_str($initData, $params);

        if (!isset($params['user'])) {
            // Fallback для тестирования
            if (strpos($initData, 'test') !== false) {
                return '12345';
            }
            throw new Exception('Параметр user не найден в initData');
        }

        $userData = json_decode($params['user'], true);

        if (!$userData || !isset($userData['id'])) {
            throw new Exception('ID пользователя не найден в данных user');
        }

        // Возвращаем как строку для консистентности
        return (string)$userData['id'];

    } catch (Exception $e) {
        error_log('extractUserIdFromInitData ERROR: ' . $e->getMessage());
        // Fallback для тестирования
        return '12345';
    }
}

/**
 * Анализирует VPN/Proxy соединения
 */
function analyzeVPN($input) {
    try {
        if (!isset($input['ip'])) {
            throw new Exception('Отсутствует IP адрес для анализа');
        }

        $ip = $input['ip'];
        $fingerprint = $input['fingerprint'] ?? 'unknown';

        error_log("analyzeVPN INFO: Анализируем IP $ip");

        // Получаем реальный IP пользователя
        $real_ip = getRealIpAddr();

        $vpn_analysis = [
            'ip' => $ip,
            'real_ip' => $real_ip,
            'fingerprint' => $fingerprint,
            'timestamp' => date('Y-m-d H:i:s'),
            'is_vpn' => false,
            'risk_score' => 0,
            'detection_methods' => [],
            'country' => 'unknown',
            'isp' => 'unknown',
            'analysis_results' => []
        ];

        // Простая проверка на известные VPN IP диапазоны
        $vpn_ranges = [
            '*******' => ['provider' => 'Google DNS', 'risk' => 'low'],
            '*******' => ['provider' => 'Cloudflare DNS', 'risk' => 'low'],
            '10.0.0.0/8' => ['provider' => 'Private Network', 'risk' => 'high'],
            '***********/16' => ['provider' => 'Private Network', 'risk' => 'high'],
            '**********/12' => ['provider' => 'Private Network', 'risk' => 'high']
        ];

        // Проверяем IP на подозрительность
        foreach ($vpn_ranges as $range => $info) {
            if (strpos($range, '/') !== false) {
                // CIDR проверка (упрощенная)
                if (strpos($ip, explode('.', explode('/', $range)[0])[0]) === 0) {
                    $vpn_analysis['is_vpn'] = true;
                    $vpn_analysis['risk_score'] += ($info['risk'] === 'high') ? 80 : 20;
                    $vpn_analysis['detection_methods'][] = 'IP_RANGE_CHECK';
                    $vpn_analysis['isp'] = $info['provider'];
                    break;
                }
            } else {
                if ($ip === $range) {
                    $vpn_analysis['risk_score'] += ($info['risk'] === 'high') ? 80 : 10;
                    $vpn_analysis['detection_methods'][] = 'KNOWN_IP_CHECK';
                    $vpn_analysis['isp'] = $info['provider'];
                    break;
                }
            }
        }

        // Дополнительные проверки
        if ($ip !== $real_ip) {
            $vpn_analysis['detection_methods'][] = 'IP_MISMATCH';
            $vpn_analysis['risk_score'] += 30;
        }

        // Проверка на localhost/private IP
        if (in_array($ip, ['127.0.0.1', 'localhost', '::1'])) {
            $vpn_analysis['detection_methods'][] = 'LOCALHOST_DETECTED';
            $vpn_analysis['risk_score'] += 50;
        }

        // Определяем финальный статус VPN
        if ($vpn_analysis['risk_score'] >= 50) {
            $vpn_analysis['is_vpn'] = true;
        }

        // Сохраняем результаты анализа
        $vpn_log_file = __DIR__ . '/../database/vpn_analysis_log.json';
        $vpn_logs = [];

        if (file_exists($vpn_log_file)) {
            $vpn_logs = json_decode(file_get_contents($vpn_log_file), true) ?: [];
        }

        $vpn_logs[] = $vpn_analysis;

        // Ограничиваем размер лога
        if (count($vpn_logs) > 1000) {
            $vpn_logs = array_slice($vpn_logs, -1000);
        }

        file_put_contents($vpn_log_file, json_encode($vpn_logs, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

        return [
            'success' => true,
            'message' => 'VPN анализ завершен',
            'vpn_detected' => $vpn_analysis['is_vpn'],
            'risk_score' => $vpn_analysis['risk_score'],
            'detection_methods' => $vpn_analysis['detection_methods'],
            'country' => $vpn_analysis['country'],
            'isp' => $vpn_analysis['isp'],
            'analysis_data' => $vpn_analysis
        ];

    } catch (Exception $e) {
        error_log('analyzeVPN ERROR: ' . $e->getMessage());
        return [
            'success' => false,
            'error' => 'Ошибка анализа VPN: ' . $e->getMessage()
        ];
    }
}

/**
 * Получает список отпечатков устройств с пагинацией и фильтрацией
 */
function getDeviceFingerprints($input) {
    try {
        $page = max(1, (int)($input['page'] ?? 1));
        $limit = max(1, min(100, (int)($input['limit'] ?? 20)));
        $search = $input['search'] ?? '';
        $device_type = $input['device_type'] ?? '';
        $risk_level = $input['risk_level'] ?? '';
        $sort_by = $input['sort_by'] ?? 'created_at';
        $sort_order = $input['sort_order'] ?? 'desc';

        // Загружаем данные отпечатков
        $advanced_fingerprints_file = __DIR__ . '/../database/advanced_fingerprints.json';
        $fingerprints = [];

        if (file_exists($advanced_fingerprints_file)) {
            $fingerprints = json_decode(file_get_contents($advanced_fingerprints_file), true) ?: [];
        }

        // Преобразуем в массив для удобства работы
        $fingerprints_array = [];
        foreach ($fingerprints as $fingerprint => $data) {
            $device_info = analyzeDeviceType($data['components'] ?? []);

            $fingerprints_array[] = [
                'fingerprint' => $fingerprint,
                'user_id' => $data['user_id'] ?? 'unknown',
                'ip_address' => $data['ip_address'] ?? 'unknown',
                'timestamp' => $data['timestamp'] ?? '',
                'created_at' => $data['created_at'] ?? 0,
                'risk_score' => $data['risk_score'] ?? 0,
                'device_type' => $device_info['type'],
                'device_info' => $device_info,
                'platform' => $data['components']['system']['platform'] ?? 'unknown',
                'user_agent' => $data['components']['system']['userAgent'] ?? 'unknown',
                'screen_resolution' => ($data['components']['screen']['width'] ?? 0) . 'x' . ($data['components']['screen']['height'] ?? 0),
                'risk_level' => getRiskLevel($data['risk_score'] ?? 0)
            ];
        }

        // Применяем фильтры
        if (!empty($search)) {
            $fingerprints_array = array_filter($fingerprints_array, function($item) use ($search) {
                return stripos($item['fingerprint'], $search) !== false ||
                       stripos($item['user_id'], $search) !== false ||
                       stripos($item['ip_address'], $search) !== false ||
                       stripos($item['user_agent'], $search) !== false;
            });
        }

        if (!empty($device_type)) {
            $fingerprints_array = array_filter($fingerprints_array, function($item) use ($device_type) {
                return $item['device_type'] === $device_type;
            });
        }

        if (!empty($risk_level)) {
            $fingerprints_array = array_filter($fingerprints_array, function($item) use ($risk_level) {
                return $item['risk_level'] === $risk_level;
            });
        }

        // Сортировка
        usort($fingerprints_array, function($a, $b) use ($sort_by, $sort_order) {
            $val_a = $a[$sort_by] ?? 0;
            $val_b = $b[$sort_by] ?? 0;

            if ($sort_order === 'desc') {
                return $val_b <=> $val_a;
            } else {
                return $val_a <=> $val_b;
            }
        });

        // Подсчитываем общее количество
        $total = count($fingerprints_array);
        $total_pages = ceil($total / $limit);

        // Применяем пагинацию
        $offset = ($page - 1) * $limit;
        $fingerprints_page = array_slice($fingerprints_array, $offset, $limit);

        // Статистика по типам устройств
        $device_stats = [
            'desktop' => 0,
            'mobile' => 0,
            'tablet' => 0,
            'unknown' => 0
        ];

        foreach ($fingerprints_array as $item) {
            $device_stats[$item['device_type']]++;
        }

        return [
            'success' => true,
            'data' => $fingerprints_page,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => $total,
                'total_pages' => $total_pages,
                'has_next' => $page < $total_pages,
                'has_prev' => $page > 1
            ],
            'filters' => [
                'search' => $search,
                'device_type' => $device_type,
                'risk_level' => $risk_level,
                'sort_by' => $sort_by,
                'sort_order' => $sort_order
            ],
            'stats' => [
                'total_fingerprints' => $total,
                'device_types' => $device_stats,
                'risk_distribution' => calculateRiskDistribution($fingerprints_array)
            ]
        ];

    } catch (Exception $e) {
        error_log('getDeviceFingerprints ERROR: ' . $e->getMessage());
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Экспортирует отпечатки устройств в CSV формат
 */
function exportDeviceFingerprints($input) {
    try {
        $format = $input['format'] ?? 'csv';
        $filters = $input['filters'] ?? [];

        // Получаем все данные без пагинации
        $all_data_input = array_merge($filters, ['limit' => 10000, 'page' => 1]);
        $result = getDeviceFingerprints($all_data_input);

        if (!$result['success']) {
            throw new Exception('Ошибка получения данных для экспорта');
        }

        $data = $result['data'];

        if ($format === 'csv') {
            $csv_content = generateCSVContent($data);

            return [
                'success' => true,
                'format' => 'csv',
                'filename' => 'device_fingerprints_' . date('Y-m-d_H-i-s') . '.csv',
                'content' => $csv_content,
                'total_records' => count($data)
            ];
        } else {
            throw new Exception('Неподдерживаемый формат экспорта');
        }

    } catch (Exception $e) {
        error_log('exportDeviceFingerprints ERROR: ' . $e->getMessage());
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Анализирует тип устройства на основе компонентов отпечатка
 */
function analyzeDeviceType($components) {
    $device_info = [
        'type' => 'unknown',
        'is_mobile' => false,
        'is_tablet' => false,
        'is_desktop' => false,
        'details' => []
    ];

    try {
        // Анализируем User Agent
        $user_agent = $components['system']['userAgent'] ?? '';
        $platform = $components['system']['platform'] ?? '';
        $max_touch_points = $components['hardware']['maxTouchPoints'] ?? 0;
        $screen_width = $components['screen']['width'] ?? 0;
        $screen_height = $components['screen']['height'] ?? 0;

        // Определяем тип устройства по User Agent
        if (preg_match('/Mobile|Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i', $user_agent)) {
            if (preg_match('/iPad/i', $user_agent) || ($screen_width >= 768 && $max_touch_points > 0)) {
                $device_info['type'] = 'tablet';
                $device_info['is_tablet'] = true;
            } else {
                $device_info['type'] = 'mobile';
                $device_info['is_mobile'] = true;
            }
        } else {
            $device_info['type'] = 'desktop';
            $device_info['is_desktop'] = true;
        }

        // Дополнительная проверка по размеру экрана и touch points
        if ($max_touch_points > 0 && $device_info['type'] === 'desktop') {
            if ($screen_width >= 768) {
                $device_info['type'] = 'tablet';
                $device_info['is_tablet'] = true;
                $device_info['is_desktop'] = false;
            } else {
                $device_info['type'] = 'mobile';
                $device_info['is_mobile'] = true;
                $device_info['is_desktop'] = false;
            }
        }

        // Добавляем детали
        $device_info['details'] = [
            'platform' => $platform,
            'screen_size' => $screen_width . 'x' . $screen_height,
            'touch_points' => $max_touch_points,
            'user_agent_snippet' => substr($user_agent, 0, 100)
        ];

    } catch (Exception $e) {
        error_log('analyzeDeviceType ERROR: ' . $e->getMessage());
    }

    return $device_info;
}

/**
 * Определяет уровень риска на основе числового значения
 */
function getRiskLevel($risk_score) {
    if ($risk_score >= 80) return 'critical';
    if ($risk_score >= 60) return 'high';
    if ($risk_score >= 40) return 'medium';
    if ($risk_score >= 20) return 'low';
    return 'minimal';
}

/**
 * Вычисляет распределение рисков
 */
function calculateRiskDistribution($fingerprints_array) {
    $distribution = [
        'minimal' => 0,
        'low' => 0,
        'medium' => 0,
        'high' => 0,
        'critical' => 0
    ];

    foreach ($fingerprints_array as $item) {
        $level = $item['risk_level'];
        if (isset($distribution[$level])) {
            $distribution[$level]++;
        }
    }

    return $distribution;
}

/**
 * Генерирует CSV контент для экспорта
 */
function generateCSVContent($data) {
    $csv_lines = [];

    // Заголовки
    $headers = [
        'Отпечаток',
        'ID пользователя',
        'IP адрес',
        'Дата создания',
        'Тип устройства',
        'Платформа',
        'Разрешение экрана',
        'Уровень риска',
        'Баллы риска',
        'User Agent'
    ];

    $csv_lines[] = implode(',', array_map(function($header) {
        return '"' . str_replace('"', '""', $header) . '"';
    }, $headers));

    // Данные
    foreach ($data as $item) {
        $row = [
            substr(strval($item['fingerprint']), 0, 16) . '...',
            $item['user_id'],
            $item['ip_address'],
            $item['timestamp'],
            $item['device_type'],
            $item['platform'],
            $item['screen_resolution'],
            $item['risk_level'],
            $item['risk_score'],
            substr($item['user_agent'], 0, 100) . '...'
        ];

        $csv_lines[] = implode(',', array_map(function($field) {
            return '"' . str_replace('"', '""', $field) . '"';
        }, $row));
    }

    return implode("\n", $csv_lines);
}

/**
 * Сохраняет отпечаток устройства в файл advanced_fingerprints.json
 */
function saveAdvancedFingerprint($userId, $fingerprintData, $fraudAnalysis) {
    try {
        $fingerprintsFile = __DIR__ . '/../database/advanced_fingerprints.json';

        // Загружаем существующие отпечатки
        $fingerprints = [];
        if (file_exists($fingerprintsFile)) {
            $content = file_get_contents($fingerprintsFile);
            $fingerprints = json_decode($content, true) ?: [];
        }

        // Создаем запись отпечатка
        $fingerprintRecord = [
            'user_id' => $userId,
            'fingerprint' => $fingerprintData['fingerprint'],
            'components' => $fingerprintData['components'],
            'ip_address' => getRealIpAddr(),
            'timestamp' => time(),
            'created_at' => date('Y-m-d H:i:s'),
            'device_type' => $fingerprintData['components']['device_type'] ?? 'unknown',
            'platform' => $fingerprintData['components']['platform'] ?? 'unknown',
            'screen_resolution' => $fingerprintData['components']['screen_resolution'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'risk_level' => $fraudAnalysis['risk_level'] ?? 'LOW',
            'risk_score' => $fraudAnalysis['risk_score'] ?? 0,
            'fraud_indicators' => $fraudAnalysis['indicators'] ?? []
        ];

        // Добавляем отпечаток
        $fingerprints[$fingerprintData['fingerprint']] = $fingerprintRecord;

        // Ограничиваем количество отпечатков (максимум 10000)
        if (count($fingerprints) > 10000) {
            // Удаляем самые старые отпечатки
            uasort($fingerprints, function($a, $b) {
                return ($b['timestamp'] ?? 0) - ($a['timestamp'] ?? 0);
            });
            $fingerprints = array_slice($fingerprints, 0, 10000, true);
        }

        // Сохраняем файл
        $result = file_put_contents($fingerprintsFile, json_encode($fingerprints, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

        if ($result === false) {
            error_log("saveAdvancedFingerprint ERROR: Не удалось сохранить файл отпечатков");
            return false;
        }

        error_log("saveAdvancedFingerprint INFO: Отпечаток пользователя {$userId} сохранен");
        return true;

    } catch (Exception $e) {
        error_log("saveAdvancedFingerprint ERROR: " . $e->getMessage());
        return false;
    }
}

?>
