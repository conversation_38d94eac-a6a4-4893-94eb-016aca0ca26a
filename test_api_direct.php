<?php
/**
 * Прямой тест API fraud-detection.php
 */

echo "=== ТЕСТ API fraud-detection.php ===\n";

// Подключаем API напрямую
$_POST['action'] = 'register_device';
$_POST['initData'] = 'test_fallback_mode_12345';
$_POST['fingerprint_data'] = json_encode([
    'fingerprint' => 'test_fingerprint_' . time(),
    'components' => [
        'device_type' => 'desktop',
        'platform' => 'Windows',
        'screen_resolution' => '1920x1080',
        'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'timezone' => 'Europe/Moscow',
        'language' => 'ru-RU',
        'canvas_fingerprint' => 'test_canvas_' . time(),
        'webgl_fingerprint' => 'test_webgl_' . time()
    ]
]);
$_POST['vpn_data'] = json_encode([
    'is_vpn_detected' => false,
    'risk_score' => 10,
    'indicators' => []
]);

echo "Данные для теста:\n";
echo "action: " . $_POST['action'] . "\n";
echo "initData: " . $_POST['initData'] . "\n";
echo "fingerprint_data: " . $_POST['fingerprint_data'] . "\n";
echo "vpn_data: " . $_POST['vpn_data'] . "\n\n";

echo "Запускаем API...\n";

// Захватываем вывод
ob_start();
include 'api/fraud-detection.php';
$output = ob_get_clean();

echo "Результат API:\n";
echo $output . "\n";

// Проверяем файлы
echo "\n=== ПРОВЕРКА ФАЙЛОВ ===\n";

$userDataFile = 'database/user_data.json';
if (file_exists($userDataFile)) {
    $userData = json_decode(file_get_contents($userDataFile), true);
    if (isset($userData['12345'])) {
        echo "✅ Пользователь 12345 создан в user_data.json\n";
    } else {
        echo "❌ Пользователь 12345 НЕ найден в user_data.json\n";
    }
} else {
    echo "❌ Файл user_data.json не найден\n";
}

$fingerprintsFile = 'database/advanced_fingerprints.json';
if (file_exists($fingerprintsFile)) {
    $fingerprints = json_decode(file_get_contents($fingerprintsFile), true);
    if (!empty($fingerprints)) {
        echo "✅ Отпечатки найдены в advanced_fingerprints.json (" . count($fingerprints) . " шт.)\n";
        echo "Последний отпечаток:\n";
        $lastFingerprint = array_slice($fingerprints, -1, 1, true);
        print_r($lastFingerprint);
    } else {
        echo "❌ Отпечатки НЕ найдены в advanced_fingerprints.json\n";
    }
} else {
    echo "❌ Файл advanced_fingerprints.json не найден\n";
}

echo "\n=== КОНЕЦ ТЕСТА ===\n";
?>
