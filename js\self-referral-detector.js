/**
 * js/self-referral-detector.js
 * Система детекции самореферралов и фейковых рефералов
 * 
 * Анализирует цепочки рефералов и обнаруживает подозрительные паттерны:
 * - Самореферралы (пользователь ссылается на себя)
 * - Циклические ссылки (A -> B -> A)
 * - Подозрительные паттерны регистрации
 * - Дублирование отпечатков устройств между рефералами
 */

class SelfReferralDetector {
    constructor() {
        this.suspiciousPatterns = [];
        this.referralChains = new Map();
        this.deviceFingerprints = new Map();
        
        console.log('[SelfReferralDetector] 🔍 Инициализация детектора самореферралов');
    }
    
    /**
     * Анализирует реферральные связи пользователя через серверный API
     */
    async analyzeReferralRelations(initData, fingerprint) {
        try {
            // Извлекаем user_id из initData
            const userId = this.extractUserIdFromInitData(initData);

            console.log(`[SelfReferralDetector] 🔍 Анализируем реферальные связи для пользователя ${userId}`);

            // Отправляем запрос на серверный API
            const response = await fetch('/api/fraud-detection.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    action: 'analyze_referrals',
                    user_id: userId,
                    fingerprint: fingerprint,
                    init_data: initData
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();

            if (!result.success) {
                throw new Error(result.error || 'Ошибка анализа реферралов');
            }

            const analysis = result.data || result;

            // Адаптируем результат под ожидаемый формат
            const adaptedResult = {
                should_block: analysis.should_block || false,
                risk_score: analysis.risk_score || 0,
                violations: analysis.violations || [],
                recommendations: analysis.recommendations || [],
                user_id: userId,
                is_suspicious: analysis.should_block || analysis.risk_score >= 50
            };

            console.log(`[SelfReferralDetector] 📊 Серверный анализ завершен для ${userId}:`, {
                should_block: adaptedResult.should_block,
                risk_score: adaptedResult.risk_score,
                violations_count: adaptedResult.violations.length
            });

            return adaptedResult;

        } catch (error) {
            console.error('[SelfReferralDetector] ❌ Ошибка анализа реферальных связей:', error);
            throw error;
        }
    }

    /**
     * Извлекает user_id из initData Telegram
     */
    extractUserIdFromInitData(initData) {
        try {
            if (!initData) {
                throw new Error('initData не предоставлен');
            }

            // Парсим initData
            const params = new URLSearchParams(initData);
            const userParam = params.get('user');

            if (!userParam) {
                // Попробуем альтернативный способ парсинга для тестовых данных
                try {
                    // Проверяем, есть ли уже декодированные данные
                    const decoded = decodeURIComponent(initData);
                    const userMatch = decoded.match(/user=([^&]+)/);

                    if (userMatch) {
                        const userData = JSON.parse(decodeURIComponent(userMatch[1]));
                        if (userData.id) {
                            return String(userData.id);
                        }
                    }

                    // Если это тестовые данные с прямым JSON
                    if (initData.includes('"id"')) {
                        const jsonMatch = initData.match(/\{[^}]+\}/);
                        if (jsonMatch) {
                            const userData = JSON.parse(jsonMatch[0]);
                            if (userData.id) {
                                return String(userData.id);
                            }
                        }
                    }
                } catch (parseError) {
                    console.log('[SelfReferralDetector] ⚠️ Альтернативный парсинг не удался:', parseError);
                }

                throw new Error('Параметр user не найден в initData');
            }

            const userData = JSON.parse(decodeURIComponent(userParam));

            if (!userData.id) {
                throw new Error('ID пользователя не найден в данных user');
            }

            // Возвращаем как строку для консистентности
            return String(userData.id);

        } catch (error) {
            console.error('[SelfReferralDetector] ❌ Ошибка извлечения user_id:', error);
            throw new Error('Не удалось извлечь user_id из initData');
        }
    }
    
    /**
     * Проверяет попытку самореферрала
     */
    async checkSelfReferral(userId, referrerId) {
        // Прямая проверка: пользователь пытается стать рефералом самого себя
        if (userId === referrerId) {
            console.log(`[SelfReferralDetector] 🚨 Обнаружен самореферрал: ${userId} -> ${referrerId}`);
            return true;
        }
        
        // Проверка через строковое сравнение (на случай разных типов)
        if (String(userId) === String(referrerId)) {
            console.log(`[SelfReferralDetector] 🚨 Обнаружен самореферрал (строки): ${userId} -> ${referrerId}`);
            return true;
        }
        
        return false;
    }
    
    /**
     * Проверяет циклические реферальные ссылки
     */
    async checkCyclicReferrals(userId, referrerId) {
        try {
            // Получаем данные о рефералах с сервера
            const response = await fetch('/api/fraud-detection.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    action: 'check_referral_chain',
                    user_id: userId,
                    referrer_id: referrerId
                })
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const result = await response.json();
            
            if (result.success && result.chain_analysis) {
                return {
                    is_cyclic: result.chain_analysis.is_cyclic,
                    chain: result.chain_analysis.chain || [],
                    depth: result.chain_analysis.depth || 0
                };
            }
            
            return { is_cyclic: false, chain: [], depth: 0 };
            
        } catch (error) {
            console.error('[SelfReferralDetector] ❌ Ошибка проверки циклических ссылок:', error);
            return { is_cyclic: false, chain: [], depth: 0 };
        }
    }
    
    /**
     * Проверяет дублирование отпечатков устройств
     */
    async checkFingerprintDuplication(userId, referrerId, fingerprintData) {
        try {
            const response = await fetch('/api/fraud-detection.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    action: 'check_fingerprint_duplication',
                    user_id: userId,
                    referrer_id: referrerId,
                    fingerprint: fingerprintData.fingerprint
                })
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const result = await response.json();
            
            return {
                is_duplicate: result.is_duplicate || false,
                similarity_score: result.similarity_score || 0,
                matching_users: result.matching_users || []
            };
            
        } catch (error) {
            console.error('[SelfReferralDetector] ❌ Ошибка проверки отпечатков:', error);
            return { is_duplicate: false, similarity_score: 0, matching_users: [] };
        }
    }
    
    /**
     * Проверяет подозрительные временные паттерны
     */
    async checkSuspiciousTiming(userId, referrerId) {
        try {
            const response = await fetch('/api/fraud-detection.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    action: 'check_timing_patterns',
                    user_id: userId,
                    referrer_id: referrerId
                })
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const result = await response.json();
            
            return {
                is_suspicious: result.is_suspicious || false,
                reason: result.reason || '',
                time_diff: result.time_diff || 0
            };
            
        } catch (error) {
            console.error('[SelfReferralDetector] ❌ Ошибка проверки временных паттернов:', error);
            return { is_suspicious: false, reason: '', time_diff: 0 };
        }
    }
    
    /**
     * Проверяет подозрительные IP паттерны
     */
    async checkIPPatterns(userId, referrerId) {
        try {
            const response = await fetch('/api/fraud-detection.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    action: 'check_ip_patterns',
                    user_id: userId,
                    referrer_id: referrerId
                })
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const result = await response.json();
            
            return {
                is_suspicious: result.is_suspicious || false,
                reason: result.reason || '',
                ip_similarity: result.ip_similarity || 0
            };
            
        } catch (error) {
            console.error('[SelfReferralDetector] ❌ Ошибка проверки IP паттернов:', error);
            return { is_suspicious: false, reason: '', ip_similarity: 0 };
        }
    }
    
    /**
     * Получает статистику подозрительных рефералов
     */
    async getSuspiciousReferralStats() {
        try {
            const response = await fetch('/api/fraud-detection.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    action: 'get_suspicious_referral_stats'
                })
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const result = await response.json();
            return result.stats || {};
            
        } catch (error) {
            console.error('[SelfReferralDetector] ❌ Ошибка получения статистики:', error);
            return {};
        }
    }
}

// Экспортируем класс в глобальную область видимости
window.SelfReferralDetector = SelfReferralDetector;

console.log('✅ [SelfReferralDetector] Модуль детекции самореферралов загружен');
