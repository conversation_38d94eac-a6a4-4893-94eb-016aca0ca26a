<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест исправлений антифрод системы</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-section {
            background: #2a2a2a;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid #4CAF50;
        }
        .error {
            border-left-color: #f44336;
        }
        .warning {
            border-left-color: #ff9800;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #45a049;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            background: #333;
            border-radius: 4px;
        }
        .log {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🛡️ Тест исправлений антифрод системы</h1>
    
    <div class="test-section">
        <h3>📊 Статус системы</h3>
        <div id="systemStatus" class="status">Загрузка...</div>
        <button onclick="checkSystemStatus()">Обновить статус</button>
    </div>
    
    <div class="test-section">
        <h3>🎯 Тест просмотра рекламы</h3>
        <div id="adTestStatus" class="status">Готов к тестированию</div>
        <button onclick="testAdView()">Тест просмотра рекламы</button>
        <button onclick="testMultipleAdViews()">Тест множественных просмотров</button>
    </div>
    
    <div class="test-section">
        <h3>🔍 Тест API подключения</h3>
        <div id="apiTestStatus" class="status">Готов к тестированию</div>
        <button onclick="testApiConnection()">Тест API</button>
        <button onclick="testFraudCheck()">Тест проверки фрода</button>
    </div>
    
    <div class="test-section">
        <h3>📝 Лог консоли</h3>
        <div id="consoleLog" class="log"></div>
        <button onclick="clearLog()">Очистить лог</button>
    </div>

    <script>
        // Перехватываем console.log для отображения в интерфейсе
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function addToLog(message, type = 'log') {
            const logDiv = document.getElementById('consoleLog');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'warn' ? '⚠️' : '📝';
            logDiv.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToLog(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToLog(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToLog(args.join(' '), 'warn');
        };
        
        // Функции тестирования
        function checkSystemStatus() {
            const statusDiv = document.getElementById('systemStatus');
            
            if (window.fraudManager) {
                const status = window.fraudManager.getStatus();
                statusDiv.innerHTML = `
                    <strong>FraudManager:</strong> ${status.initialized ? '✅ Инициализирован' : '❌ Не инициализирован'}<br>
                    <strong>Пользователь заблокирован:</strong> ${status.userBlocked ? '🚫 Да' : '✅ Нет'}<br>
                    <strong>Отпечаток устройства:</strong> ${status.hasFingerprint ? '✅ Есть' : '❌ Нет'}<br>
                    <strong>ID отпечатка:</strong> ${status.fingerprintId || 'Не создан'}<br>
                    <strong>Причина блокировки:</strong> ${status.blockReason || 'Нет'}
                `;
            } else {
                statusDiv.innerHTML = '❌ FraudManager не найден';
            }
        }
        
        function testAdView() {
            const statusDiv = document.getElementById('adTestStatus');
            console.log('[TEST] 🎯 Начинаем тест просмотра рекламы...');
            
            if (window.fraudManager) {
                const canView = window.fraudManager.canPerformAction('view_ad');
                statusDiv.innerHTML = `Результат: ${canView ? '✅ Разрешено' : '🚫 Заблокировано'}`;
                console.log(`[TEST] Результат canPerformAction('view_ad'): ${canView}`);
            } else {
                statusDiv.innerHTML = '❌ FraudManager не найден';
                console.error('[TEST] FraudManager не найден');
            }
        }
        
        function testMultipleAdViews() {
            console.log('[TEST] 🎯 Тест множественных просмотров рекламы...');
            
            for (let i = 1; i <= 5; i++) {
                setTimeout(() => {
                    console.log(`[TEST] Попытка просмотра рекламы #${i}`);
                    testAdView();
                }, i * 1000);
            }
        }
        
        async function testApiConnection() {
            const statusDiv = document.getElementById('apiTestStatus');
            console.log('[TEST] 🔍 Тестируем подключение к API...');
            
            try {
                const response = await fetch('api/fraud-detection.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'test_connection'
                    })
                });
                
                if (response.ok) {
                    const result = await response.json();
                    statusDiv.innerHTML = `✅ API доступен: ${JSON.stringify(result)}`;
                    console.log('[TEST] API ответ:', result);
                } else {
                    statusDiv.innerHTML = `❌ HTTP ошибка: ${response.status}`;
                    console.error('[TEST] HTTP ошибка:', response.status, response.statusText);
                }
            } catch (error) {
                statusDiv.innerHTML = `❌ Ошибка подключения: ${error.message}`;
                console.error('[TEST] Ошибка API:', error);
            }
        }
        
        async function testFraudCheck() {
            console.log('[TEST] 🛡️ Тестируем проверку фрода...');
            
            try {
                const response = await fetch('api/fraud-detection.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'check_fraud',
                        initData: 'test_data'
                    })
                });
                
                const result = await response.json();
                console.log('[TEST] Результат проверки фрода:', result);
            } catch (error) {
                console.error('[TEST] Ошибка проверки фрода:', error);
            }
        }
        
        function clearLog() {
            document.getElementById('consoleLog').textContent = '';
        }
        
        // Автоматическая проверка при загрузке
        window.addEventListener('load', () => {
            console.log('[TEST] 🚀 Страница загружена, начинаем тестирование...');
            setTimeout(checkSystemStatus, 2000);
        });
    </script>
    
    <!-- Подключаем fraud систему -->
    <script src="js/fraud-blocker.js"></script>
    <script src="js/device-fingerprint.js"></script>
    <script src="js/vpn-detector.js"></script>
    <script src="js/fraud-manager.js"></script>
</body>
</html>
