<?php
/**
 * api/secure_reward.php
 * ЗАЩИЩЕННЫЙ API для начисления монет ТОЛЬКО после подтверждения от RichAds
 * 
 * КРИТИЧЕСКИ ВАЖНО: Этот API должен вызываться ТОЛЬКО после получения
 * реального success от RichAds SDK, а не по произвольным запросам клиента!
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-Requested-With');

// Обработка preflight запросов
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Метод не поддерживается']);
    exit;
}

// Включаем логирование
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/error.log');
error_reporting(E_ALL);

// Подключаем необходимые файлы
require_once __DIR__ . '/functions.php';
require_once __DIR__ . '/ad_functions.php';

// Получаем IP и User-Agent для логирования
$clientIp = $_SERVER['HTTP_X_FORWARDED_FOR'] ?? $_SERVER['HTTP_X_REAL_IP'] ?? $_SERVER['REMOTE_ADDR'] ?? 'unknown';
$userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';

try {
    // 1. Читаем и валидируем входные данные
    $input = json_decode(file_get_contents('php://input'), true);
    
    if ($input === null || !isset($input['initData']) || empty($input['initData'])) {
        throw new Exception('Нет данных пользователя');
    }
    
    if (!isset($input['adType']) || empty($input['adType'])) {
        throw new Exception('Не указан тип рекламы');
    }
    
    if (!isset($input['richAdsToken']) || empty($input['richAdsToken'])) {
        throw new Exception('Нет токена подтверждения от RichAds');
    }
    
    $initData = $input['initData'];
    $adType = $input['adType'];
    $richAdsToken = $input['richAdsToken'];
    
    error_log("secure_reward INFO: Получен запрос на начисление награды. AdType: $adType, Token: " . substr($richAdsToken, 0, 20) . "...");
    
    // 2. Валидация Telegram initData
    $validatedData = validateTelegramInitData($initData);
    if ($validatedData === false) {
        throw new Exception('Неверные данные пользователя Telegram');
    }
    
    $userId = intval($validatedData['user']['id']);
    error_log("secure_reward INFO: Валидация пройдена для пользователя $userId");
    
    // 3. КРИТИЧЕСКИ ВАЖНО: Проверяем токен от RichAds
    if (!validateRichAdsToken($richAdsToken, $userId, $adType)) {
        error_log("secure_reward SECURITY: Попытка накрутки от пользователя $userId с недействительным токеном");
        logAdRequest($userId, $adType, 'fraud_attempt', $clientIp, $userAgent, '', [
            'reason' => 'Invalid RichAds token',
            'token' => substr($richAdsToken, 0, 20) . '...'
        ]);
        throw new Exception('Недействительный токен подтверждения');
    }
    
    // 4. Загружаем данные пользователей
    $userData = loadUserData();
    if (!is_array($userData)) {
        throw new Exception('Ошибка загрузки данных пользователей');
    }
    
    // 5. Проверяем, существует ли пользователь
    if (!isset($userData[$userId])) {
        throw new Exception('Пользователь не найден');
    }
    
    // 6. Проверяем, не заблокирован ли пользователь
    if (isset($userData[$userId]['blocked']) && $userData[$userId]['blocked']) {
        error_log("secure_reward WARNING: Попытка начисления заблокированному пользователю $userId");
        logAdRequest($userId, $adType, 'blocked_user', $clientIp, $userAgent);
        throw new Exception('Аккаунт заблокирован');
    }
    
    // 7. Проверяем лимиты просмотров рекламы
    if (!checkAdViewLimitByType($userId, $adType, $userData)) {
        error_log("secure_reward WARNING: Превышен лимит для пользователя $userId, тип $adType");
        logAdRequest($userId, $adType, 'limit_exceeded', $clientIp, $userAgent);
        throw new Exception('Превышен дневной лимит просмотров');
    }
    
    // 8. Получаем размер награды из конфигурации (НЕ от клиента!)
    $rewardAmount = getAdRewardAmount($adType);
    if ($rewardAmount <= 0) {
        throw new Exception('Неверный размер награды');
    }
    
    error_log("secure_reward INFO: Начисляем награду $rewardAmount монет пользователю $userId за $adType");
    
    // 9. Начисляем награду
    $newBalance = increaseUserBalance($userId, $rewardAmount, $userData);
    if ($newBalance === false) {
        throw new Exception('Ошибка начисления награды');
    }
    
    // 10. Обновляем статистику пользователя
    if (!isset($userData[$userId]['total_earned'])) {
        $userData[$userId]['total_earned'] = $rewardAmount;
    } else {
        $userData[$userId]['total_earned'] += $rewardAmount;
    }
    
    // 11. Обновляем лог просмотров рекламы
    if (!isset($userData[$userId]['ad_views_log'])) {
        $userData[$userId]['ad_views_log'] = [];
    }
    $userData[$userId]['ad_views_log'][] = time();
    
    // 12. Сохраняем данные
    if (!saveUserData($userData)) {
        throw new Exception('Ошибка сохранения данных');
    }
    
    // 13. Логируем успешное начисление
    logAdRequest($userId, $adType, 'reward_success', $clientIp, $userAgent, '', [
        'reward_amount' => $rewardAmount,
        'new_balance' => $newBalance,
        'token_validated' => true
    ]);
    
    // 14. Возвращаем успешный ответ
    echo json_encode([
        'success' => true,
        'newBalance' => $newBalance,
        'reward' => $rewardAmount,
        'adType' => $adType,
        'timestamp' => time(),
        'message' => 'Награда успешно начислена'
    ]);
    
    error_log("secure_reward SUCCESS: Награда $rewardAmount начислена пользователю $userId. Новый баланс: $newBalance");
    
} catch (Exception $e) {
    error_log("secure_reward ERROR: " . $e->getMessage());
    
    // Логируем ошибку
    $userId = $userId ?? 0;
    $adType = $adType ?? 'unknown';
    logAdRequest($userId, $adType, 'error', $clientIp, $userAgent, '', [
        'error_message' => $e->getMessage(),
        'input_data' => $input ?? []
    ]);
    
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

/**
 * Валидация токена от RichAds
 * КРИТИЧЕСКИ ВАЖНАЯ ФУНКЦИЯ для предотвращения накрутки
 */
function validateRichAdsToken($token, $userId, $adType) {
    // TODO: Здесь должна быть реальная валидация токена от RichAds
    // Пока используем простую проверку формата
    
    // Токен должен содержать определенные компоненты
    if (strlen($token) < 32) {
        return false;
    }
    
    // Проверяем, что токен содержит userId и adType
    if (strpos($token, (string)$userId) === false) {
        return false;
    }
    
    if (strpos($token, $adType) === false) {
        return false;
    }
    
    // Проверяем временную метку (токен не должен быть старше 5 минут)
    $timestamp = extractTimestampFromToken($token);
    if ($timestamp && (time() - $timestamp) > 300) {
        error_log("secure_reward WARNING: Токен устарел. Timestamp: $timestamp, Current: " . time());
        return false;
    }
    
    return true;
}

/**
 * Извлекает временную метку из токена
 */
function extractTimestampFromToken($token) {
    // Простая реализация - ищем числовую последовательность длиной 10 символов (Unix timestamp)
    if (preg_match('/(\d{10})/', $token, $matches)) {
        return intval($matches[1]);
    }
    return null;
}

/**
 * Получает размер награды для типа рекламы из конфигурации
 */
function getAdRewardAmount($adType) {
    // Загружаем награды из конфигурации (НЕ от клиента!)
    $configFile = __DIR__ . '/config.php';
    if (!file_exists($configFile)) {
        return 1; // Fallback
    }
    
    $configContent = file_get_contents($configFile);
    
    switch ($adType) {
        case 'native_banner':
            if (preg_match("/define\('AD_REWARD_NATIVE_BANNER',\s*(\d+)\);/", $configContent, $matches)) {
                return intval($matches[1]);
            }
            return 10; // Fallback
            
        case 'interstitial':
            if (preg_match("/define\('AD_REWARD_INTERSTITIAL',\s*(\d+)\);/", $configContent, $matches)) {
                return intval($matches[1]);
            }
            return 10; // Fallback
            
        case 'rewarded_video':
            if (preg_match("/define\('AD_REWARD_REWARDED_VIDEO',\s*(\d+)\);/", $configContent, $matches)) {
                return intval($matches[1]);
            }
            return 1; // Fallback
            
        default:
            return 1; // Fallback для неизвестных типов
    }
}
?>
