<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест API наград</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        input[type="number"] {
            width: 80px;
            padding: 5px;
            margin: 0 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏆 Тест API наград за рекламу</h1>
        
        <div class="test-section">
            <h3>📡 Получение текущих наград</h3>
            <button class="test-button" onclick="getRewards()">Получить награды (публичный API)</button>
            <button class="test-button" onclick="getCurrentConfig()">Получить конфигурацию (админ API)</button>
            <div id="get-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>💾 Сохранение наград (требует авторизации в админке)</h3>
            <div>
                <label>Native Banner: <input type="number" id="native_banner" value="10" min="1"></label>
                <label>Interstitial: <input type="number" id="interstitial" value="10" min="1"></label>
                <label>Rewarded Video: <input type="number" id="rewarded_video" value="1" min="1"></label>
            </div>
            <br>
            <button class="test-button" onclick="saveRewards()">Сохранить награды (старый API)</button>
            <button class="test-button" onclick="saveRewardsNew()">Сохранить награды (новый API)</button>
            <div id="save-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>⚙️ Тест универсального API настроек</h3>
            <div>
                <label>Конверсия (%): <input type="number" id="conversion_rate" value="0.1" step="0.01" min="0.01"></label>
                <label>Мин. вывод: <input type="number" id="min_withdrawal" value="100" min="1"></label>
                <label>Показывать комиссии: <input type="checkbox" id="show_fees" checked></label>
            </div>
            <br>
            <button class="test-button" onclick="saveAppSettings()">Сохранить настройки приложения</button>
            <div id="app-settings-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>🔄 Тест обновления reward badges</h3>
            <button class="test-button" onclick="testRewardBadges()">Обновить badges в приложении</button>
            <div id="badges-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>📊 Проверка интеграции с AdsConfig</h3>
            <button class="test-button" onclick="testAdsConfig()">Проверить AdsConfig</button>
            <div id="config-result" class="result"></div>
        </div>
    </div>

    <script>
        // Функция для отображения результатов
        function showResult(elementId, data, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `result ${type}`;
            element.textContent = typeof data === 'object' ? JSON.stringify(data, null, 2) : data;
        }

        // Получение наград (публичный API)
        async function getRewards() {
            try {
                showResult('get-result', 'Загрузка наград...', 'info');

                const response = await fetch('api/get_ad_rewards.php');
                const data = await response.json();

                if (data.success) {
                    showResult('get-result', data, 'success');
                } else {
                    showResult('get-result', data, 'error');
                }
            } catch (error) {
                showResult('get-result', `Ошибка: ${error.message}`, 'error');
            }
        }

        // Получение конфигурации (админ API)
        async function getCurrentConfig() {
            try {
                showResult('get-result', 'Загрузка конфигурации...', 'info');

                const response = await fetch('api/admin/get_current_config.php');
                const data = await response.json();

                if (data.success) {
                    showResult('get-result', data, 'success');
                } else {
                    showResult('get-result', data, 'error');
                }
            } catch (error) {
                showResult('get-result', `Ошибка: ${error.message}`, 'error');
            }
        }

        // Сохранение наград (старый API)
        async function saveRewards() {
            try {
                showResult('save-result', 'Сохранение через старый API...', 'info');

                const rewards = {
                    native_banner: parseInt(document.getElementById('native_banner').value),
                    interstitial: parseInt(document.getElementById('interstitial').value),
                    rewarded_video: parseInt(document.getElementById('rewarded_video').value)
                };

                const response = await fetch('api/admin/save_ad_rewards.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ rewards: rewards })
                });

                const data = await response.json();

                if (data.success) {
                    showResult('save-result', data, 'success');
                } else {
                    showResult('save-result', data, 'error');
                }
            } catch (error) {
                showResult('save-result', `Ошибка: ${error.message}`, 'error');
            }
        }

        // Сохранение наград (новый универсальный API)
        async function saveRewardsNew() {
            try {
                showResult('save-result', 'Сохранение через новый API...', 'info');

                const data = {
                    action: 'change_settings',
                    ad_reward_native_banner: parseInt(document.getElementById('native_banner').value),
                    ad_reward_interstitial: parseInt(document.getElementById('interstitial').value),
                    ad_reward_rewarded_video: parseInt(document.getElementById('rewarded_video').value)
                };

                const response = await fetch('api/admin/save_settings.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();

                if (result.success) {
                    showResult('save-result', result, 'success');
                } else {
                    showResult('save-result', result, 'error');
                }
            } catch (error) {
                showResult('save-result', `Ошибка: ${error.message}`, 'error');
            }
        }

        // Сохранение настроек приложения
        async function saveAppSettings() {
            try {
                showResult('app-settings-result', 'Сохранение настроек приложения...', 'info');

                const data = {
                    action: 'change_settings',
                    conversion_rate: parseFloat(document.getElementById('conversion_rate').value),
                    min_withdrawal_amount: parseInt(document.getElementById('min_withdrawal').value),
                    show_fees_to_user: document.getElementById('show_fees').checked ? '1' : '0'
                };

                const response = await fetch('api/admin/save_settings.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();

                if (result.success) {
                    showResult('app-settings-result', result, 'success');
                } else {
                    showResult('app-settings-result', result, 'error');
                }
            } catch (error) {
                showResult('app-settings-result', `Ошибка: ${error.message}`, 'error');
            }
        }

        // Тест reward badges
        async function testRewardBadges() {
            try {
                showResult('badges-result', 'Проверка...', 'info');
                
                if (window.rewardBadgesManager) {
                    await window.rewardBadgesManager.refresh();
                    const rewards = window.rewardBadgesManager.getAllRewards();
                    showResult('badges-result', `Badges обновлены: ${JSON.stringify(rewards, null, 2)}`, 'success');
                } else {
                    showResult('badges-result', 'RewardBadgesManager не найден. Откройте основное приложение.', 'error');
                }
            } catch (error) {
                showResult('badges-result', `Ошибка: ${error.message}`, 'error');
            }
        }

        // Тест AdsConfig
        function testAdsConfig() {
            try {
                showResult('config-result', 'Проверка...', 'info');
                
                if (window.AdsConfig) {
                    const adTypes = window.AdsConfig.getAllAdTypes();
                    const result = adTypes.map(type => ({
                        id: type.id,
                        reward: type.reward,
                        buttonId: type.buttonId
                    }));
                    showResult('config-result', result, 'success');
                } else {
                    showResult('config-result', 'AdsConfig не найден. Откройте основное приложение.', 'error');
                }
            } catch (error) {
                showResult('config-result', `Ошибка: ${error.message}`, 'error');
            }
        }

        // Автоматически получаем награды при загрузке
        window.addEventListener('load', () => {
            getRewards();
        });
    </script>
</body>
</html>
