<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест VPN блокировки</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-section {
            background: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #444;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .danger { background: #dc3545; }
        .danger:hover { background: #c82333; }
        .results {
            background: #333;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🌐 Тест VPN блокировки</h1>
    
    <div class="test-section">
        <h2>🧪 Тестирование VPN блокировки</h2>
        <p>Этот тест проверяет, правильно ли работает блокировка при обнаружении VPN/Proxy</p>
        
        <button onclick="testVPNBlocking()">Тест VPN блокировки</button>
        <button onclick="simulateVPNDetection()" class="danger">Симулировать обнаружение VPN</button>
        <button onclick="testVPNWarning()">Тест предупреждения VPN</button>
        <button onclick="clearBlocking()">Очистить блокировку</button>
        
        <div id="results" class="results">Готов к тестированию...</div>
    </div>

    <!-- Подключаем все необходимые скрипты -->
    <script src="js/device-fingerprint.js"></script>
    <script src="js/vpn-detector.js"></script>
    <script src="js/fraud-blocker.js"></script>
    <script src="js/fraud-manager.js"></script>

    <script>
        function log(message) {
            const results = document.getElementById('results');
            results.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
            results.scrollTop = results.scrollHeight;
            console.log(message);
        }

        async function testVPNBlocking() {
            log('🧪 Тестируем VPN блокировку...');

            try {
                if (!window.fraudBlocker) {
                    throw new Error('fraudBlocker глобальный экземпляр не доступен');
                }

                log('Используем глобальный fraudBlocker экземпляр');

                // Тестируем показ экрана VPN блокировки
                window.fraudBlocker.showBlockScreen('vpn_detected');

                log('✅ Экран VPN блокировки показан');

            } catch (error) {
                log(`❌ Ошибка теста VPN блокировки: ${error.message}`);
            }
        }

        async function simulateVPNDetection() {
            log('🚨 Симулируем обнаружение VPN через FraudManager...');
            
            try {
                if (!window.FraudManager) {
                    throw new Error('FraudManager не загружен');
                }
                
                // Создаем модифицированный VPNDetector для симуляции
                const originalVPNDetector = window.VPNDetector;
                
                // Временно заменяем VPNDetector на версию, которая всегда находит VPN
                window.VPNDetector = class MockVPNDetector {
                    async detectVPN() {
                        log('🔍 Симулируем обнаружение VPN...');
                        return {
                            is_vpn_detected: true,
                            risk_score: 85,
                            indicators: ['webrtc_leak', 'timing_anomaly', 'suspicious_dns'],
                            details: {
                                webrtc_ips: ['********', '***********'],
                                timing_score: 45,
                                dns_servers: ['*******', '*******']
                            }
                        };
                    }
                };
                
                const manager = new FraudManager();
                log('FraudManager создан с мок VPN детектором');
                
                // Тестовые данные
                const testInitData = 'user=%7B%22id%22%3A123456789%2C%22first_name%22%3A%22TestUser%22%7D&auth_date=1234567890&hash=test';
                
                const result = await manager.initialize(testInitData);
                log(`Результат инициализации: ${result}`);
                
                if (!result && manager.userBlocked) {
                    log('✅ Пользователь заблокирован из-за VPN, как и ожидалось');
                } else {
                    log('⚠️ Пользователь не был заблокирован, проверьте логику');
                }
                
                // Восстанавливаем оригинальный VPNDetector
                window.VPNDetector = originalVPNDetector;
                
            } catch (error) {
                log(`❌ Ошибка симуляции VPN: ${error.message}`);
            }
        }

        async function testVPNWarning() {
            log('⚠️ Тестируем предупреждение VPN...');

            try {
                if (!window.fraudBlocker) {
                    throw new Error('fraudBlocker глобальный экземпляр не доступен');
                }

                // Показываем предупреждающий экран
                window.fraudBlocker.showWarningScreen('VPN/Proxy обнаружен. Пожалуйста, отключите VPN для продолжения работы.');

                log('✅ Предупреждение VPN показано');

            } catch (error) {
                log(`❌ Ошибка предупреждения VPN: ${error.message}`);
            }
        }

        function clearBlocking() {
            log('🧹 Очищаем блокировку...');
            
            // Удаляем все блокирующие экраны
            const blockingScreens = document.querySelectorAll('.fraud-blocking-screen');
            blockingScreens.forEach(screen => screen.remove());
            
            // Восстанавливаем прокрутку
            document.body.style.overflow = '';
            
            log('✅ Блокировка очищена');
        }

        // Автоматическая проверка при загрузке
        window.addEventListener('load', () => {
            setTimeout(() => {
                log('🔍 Проверяем загрузку компонентов...');
                
                const components = ['DeviceFingerprint', 'VPNDetector', 'FraudBlocker', 'FraudManager'];
                let allLoaded = true;
                
                components.forEach(comp => {
                    if (window[comp]) {
                        log(`✅ ${comp} загружен`);
                    } else {
                        log(`❌ ${comp} НЕ загружен`);
                        allLoaded = false;
                    }
                });
                
                if (allLoaded) {
                    log('🎉 Все компоненты загружены, готов к тестированию VPN блокировки');
                } else {
                    log('⚠️ Не все компоненты загружены, некоторые тесты могут не работать');
                }
            }, 1000);
        });

        // Отслеживаем ошибки
        window.onerror = function(message, source, lineno, colno, error) {
            log(`🚨 JavaScript ошибка: ${message} в ${source}:${lineno}`);
            return false;
        };
    </script>
</body>
</html>
