<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест системы детекции самореферралов</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        h1 {
            text-align: center;
            color: #ffd700;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }
        
        .test-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #ffd700;
        }
        
        .test-button {
            background: linear-gradient(45deg, #ff6b35, #f7931e);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            margin: 10px 5px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);
        }
        
        .test-button:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .result {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .warning { color: #ff9800; }
        .info { color: #2196F3; }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-success { background-color: #4CAF50; }
        .status-error { background-color: #f44336; }
        .status-warning { background-color: #ff9800; }
        .status-pending { background-color: #666; }
        
        .test-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #ffd700;
        }
        
        .stat-label {
            font-size: 14px;
            opacity: 0.8;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Тест системы детекции самореферралов</h1>
        
        <div class="test-section">
            <h3>📋 Управление тестами</h3>
            <button class="test-button" onclick="runAllTests()">🚀 Запустить все тесты</button>
            <button class="test-button" onclick="testSelfReferralDetection()">🔍 Тест детекции самореферралов</button>
            <button class="test-button" onclick="testFraudManagerIntegration()">🔗 Тест интеграции с FraudManager</button>
            <button class="test-button" onclick="clearResults()">🗑️ Очистить результаты</button>
        </div>
        
        <div class="test-stats">
            <div class="stat-card">
                <div class="stat-value" id="testsRun">0</div>
                <div class="stat-label">Тестов выполнено</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="testsPassed">0</div>
                <div class="stat-label">Тестов пройдено</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="testsFailed">0</div>
                <div class="stat-label">Тестов провалено</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="riskScore">0</div>
                <div class="stat-label">Risk Score</div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📊 Результаты тестирования</h3>
            <div class="result" id="testResults">Нажмите кнопку для запуска тестов...</div>
        </div>
    </div>

    <!-- Подключаем модули -->
    <script src="js/self-referral-detector.js"></script>
    <script src="js/fraud-manager.js"></script>
    <script src="js/fraud-blocker.js"></script>

    <script>
        let testsRun = 0;
        let testsPassed = 0;
        let testsFailed = 0;
        let currentRiskScore = 0;

        function updateStats() {
            document.getElementById('testsRun').textContent = testsRun;
            document.getElementById('testsPassed').textContent = testsPassed;
            document.getElementById('testsFailed').textContent = testsFailed;
            document.getElementById('riskScore').textContent = currentRiskScore;
        }

        function log(message, type = 'info') {
            const results = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString();
            const icon = type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️';
            results.innerHTML += `[${timestamp}] ${icon} ${message}\n`;
            results.scrollTop = results.scrollHeight;
        }

        function clearResults() {
            document.getElementById('testResults').innerHTML = 'Результаты очищены...\n';
            testsRun = 0;
            testsPassed = 0;
            testsFailed = 0;
            currentRiskScore = 0;
            updateStats();
        }

        async function testSelfReferralDetection() {
            log('🔍 Запуск теста детекции самореферралов...', 'info');
            testsRun++;
            
            try {
                // Создаем экземпляр детектора
                const detector = new SelfReferralDetector();
                log('✅ SelfReferralDetector создан успешно', 'success');
                
                // Тестовые данные
                const testInitData = 'user=%7B%22id%22%3A123456789%7D&other_param=value';
                const testFingerprint = 'test_fingerprint_hash_12345';
                
                log(`📋 Тестовые данные: User ID = 123456789, Fingerprint = ${testFingerprint}`, 'info');
                
                // Мокаем fetch для тестирования
                const originalFetch = window.fetch;
                window.fetch = async (url, options) => {
                    log(`🌐 API запрос: ${options.method} ${url}`, 'info');
                    
                    return {
                        ok: true,
                        json: async () => ({
                            success: true,
                            data: {
                                should_block: true,
                                risk_score: 85,
                                violations: [
                                    {
                                        type: 'cyclic_referral',
                                        description: 'Обнаружена циклическая реферральная цепочка',
                                        risk_points: 30
                                    },
                                    {
                                        type: 'fingerprint_duplicate',
                                        description: 'Дублирующиеся отпечатки устройств',
                                        risk_points: 25
                                    },
                                    {
                                        type: 'suspicious_timing',
                                        description: 'Подозрительные временные паттерны',
                                        risk_points: 30
                                    }
                                ],
                                recommendations: [
                                    'Заблокировать пользователя',
                                    'Проверить связанные аккаунты'
                                ]
                            }
                        })
                    };
                };
                
                // Выполняем анализ
                const analysis = await detector.analyzeReferralRelations(testInitData, testFingerprint);
                
                // Восстанавливаем оригинальный fetch
                window.fetch = originalFetch;
                
                log(`📊 Анализ завершен: Should Block = ${analysis.should_block}, Risk Score = ${analysis.risk_score}`, 'info');
                currentRiskScore = analysis.risk_score;
                
                // Проверяем результаты
                if (analysis.should_block && analysis.risk_score > 0 && analysis.violations.length > 0) {
                    log('🎉 Тест детекции самореферралов ПРОЙДЕН', 'success');
                    testsPassed++;
                } else {
                    log('❌ Тест детекции самореферралов ПРОВАЛЕН', 'error');
                    testsFailed++;
                }
                
                // Выводим детали
                log(`📈 Детали анализа:`, 'info');
                log(`   - Нарушений обнаружено: ${analysis.violations.length}`, 'info');
                analysis.violations.forEach((violation, index) => {
                    log(`   ${index + 1}. ${violation.type}: ${violation.description}`, 'warning');
                });
                
            } catch (error) {
                log(`❌ Ошибка теста детекции: ${error.message}`, 'error');
                testsFailed++;
            }
            
            updateStats();
        }

        async function testFraudManagerIntegration() {
            log('🔗 Запуск теста интеграции с FraudManager...', 'info');
            testsRun++;
            
            try {
                // Проверяем наличие FraudManager
                if (typeof FraudManager === 'undefined') {
                    throw new Error('FraudManager не загружен');
                }
                
                log('✅ FraudManager доступен', 'success');
                
                // Создаем экземпляр FraudManager
                const fraudManager = new FraudManager();
                log('✅ FraudManager создан успешно', 'success');
                
                // Проверяем наличие метода checkSelfReferrals
                if (typeof fraudManager.checkSelfReferrals === 'function') {
                    log('✅ Метод checkSelfReferrals найден', 'success');
                    testsPassed++;
                } else {
                    log('❌ Метод checkSelfReferrals не найден', 'error');
                    testsFailed++;
                }
                
            } catch (error) {
                log(`❌ Ошибка теста интеграции: ${error.message}`, 'error');
                testsFailed++;
            }
            
            updateStats();
        }

        async function runAllTests() {
            log('🚀 Запуск всех тестов системы детекции самореферралов...', 'info');
            log('=' .repeat(60), 'info');
            
            clearResults();
            
            await testSelfReferralDetection();
            await testFraudManagerIntegration();
            
            log('=' .repeat(60), 'info');
            log(`🏁 Все тесты завершены. Результат: ${testsPassed}/${testsRun} пройдено`, 
                testsPassed === testsRun ? 'success' : 'warning');
        }

        // Инициализация при загрузке страницы
        window.addEventListener('load', () => {
            log('🔧 Система тестирования детекции самореферралов загружена', 'success');
            updateStats();
        });
    </script>
</body>
</html>
