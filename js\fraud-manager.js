/**
 * js/fraud-manager.js
 * Главный менеджер антифрод системы
 * 
 * Координирует работу всех компонентов антифрод защиты:
 * - Device fingerprinting
 * - Fraud detection
 * - Block screens
 * - Integration with app
 */

class FraudManager {
    constructor() {
        this.initialized = false;
        this.deviceFingerprint = null;
        this.fraudBlocker = null;
        this.fingerprintData = null;
        this.userBlocked = false;
        this.vpnDetector = null;
        this.vpnResults = null;

        console.log('[FraudManager] 🛡️ Инициализация главного менеджера антифрод системы');
    }
    
    /**
     * Инициализирует антифрод систему
     */
    async initialize(initData) {
        try {
            console.log('[FraudManager] 🚀 Запуск антифрод системы...');
            
            // Проверяем зависимости
            if (!window.DeviceFingerprint) {
                throw new Error('DeviceFingerprint модуль не загружен');
            }

            if (!window.fraudBlocker) {
                throw new Error('FraudBlocker модуль не загружен');
            }

            if (!window.VPNDetector) {
                throw new Error('VPNDetector модуль не загружен');
            }

            if (!window.SelfReferralDetector) {
                throw new Error('SelfReferralDetector модуль не загружен');
            }

            this.deviceFingerprint = new DeviceFingerprint();
            this.fraudBlocker = window.fraudBlocker;
            this.vpnDetector = new VPNDetector();
            this.selfReferralDetector = new SelfReferralDetector();
            
            // 1. Сначала проверяем текущий статус фрода
            console.log('[FraudManager] 🔍 Проверяем текущий статус фрода...');
            const isBlocked = await this.fraudBlocker.checkFraudStatus(initData);
            
            if (isBlocked) {
                this.userBlocked = true;
                console.log('[FraudManager] 🚫 Пользователь заблокирован, останавливаем инициализацию');
                return false;
            }
            
            // 2. Генерируем отпечаток устройства
            console.log('[FraudManager] 📱 Генерируем отпечаток устройства...');
            this.fingerprintData = await this.deviceFingerprint.generateFingerprint();
            
            if (!this.fingerprintData) {
                throw new Error('Не удалось создать отпечаток устройства');
            }
            
            console.log('[FraudManager] ✅ Отпечаток устройства создан:',
                       this.fingerprintData.fingerprint.substring(0, 16) + '...');

            // 3. Запускаем VPN детекцию
            console.log('[FraudManager] 🌐 Запускаем детекцию VPN/Proxy...');
            this.vpnResults = await this.vpnDetector.detectVPN();

            console.log('[FraudManager] 🔍 Результат VPN детекции:', {
                vpn_detected: this.vpnResults.is_vpn_detected,
                risk_score: this.vpnResults.risk_score,
                indicators: this.vpnResults.indicators
            });

            // 3.1. Проверяем VPN и блокируем если обнаружен
            if (this.vpnResults.is_vpn_detected) {
                console.log('[FraudManager] 🚫 VPN обнаружен, блокируем пользователя');

                // Показываем экран блокировки VPN
                this.fraudBlocker.showBlockScreen('vpn_detected');

                this.userBlocked = true;
                return false;
            }

            // 3.2. Проверяем самореферралы
            const referralCheck = await this.checkSelfReferrals(initData);
            if (referralCheck && referralCheck.should_block) {
                console.log('[FraudManager] 🚫 Обнаружен самореферрал, блокируем пользователя');

                // Показываем экран блокировки самореферрала
                this.fraudBlocker.showBlockScreen('self_referral_detected');

                this.userBlocked = true;
                return false;
            }

            // 4. Регистрируем отпечаток и проверяем на фрод
            console.log('[FraudManager] 📊 Регистрируем отпечаток и анализируем фрод...');
            const fraudResult = await this.registerFingerprintAndCheckFraud(initData);
            
            if (!fraudResult.success) {
                console.error('[FraudManager] ❌ Ошибка регистрации отпечатка:', fraudResult.error);
                return false;
            }
            
            // 4. Обрабатываем результаты анализа фрода
            const fraudAnalysis = fraudResult.fraud_analysis;
            const userStatus = fraudResult.user_status;
            
            console.log('[FraudManager] 📈 Результат анализа фрода:', {
                is_fraud: fraudAnalysis.is_fraud,
                risk_level: fraudAnalysis.risk_level,
                risk_score: fraudAnalysis.risk_score,
                blocked: userStatus.blocked
            });
            
            // 5. Если пользователь заблокирован - показываем экран блокировки
            if (userStatus.blocked) {
                this.userBlocked = true;
                console.log('[FraudManager] 🚫 Пользователь заблокирован после анализа');
                return false;
            }
            
            // 6. Если обнаружен фрод, но не критический - показываем предупреждение
            if (fraudAnalysis.is_fraud && fraudAnalysis.risk_level !== 'LOW') {
                console.log('[FraudManager] ⚠️ Обнаружен фрод, показываем предупреждение');
                this.fraudBlocker.showWarningScreen(fraudAnalysis.reason);
            }
            
            this.initialized = true;
            console.log('[FraudManager] ✅ Антифрод система успешно инициализирована');
            
            return true;
            
        } catch (error) {
            console.error('[FraudManager] ❌ Ошибка инициализации антифрод системы:', error);
            return false;
        }
    }
    
    /**
     * Регистрирует отпечаток устройства и проверяет на фрод
     */
    async registerFingerprintAndCheckFraud(initData) {
        try {
            const response = await fetch('/api/fraud-detection.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    action: 'register_device',
                    initData: initData,
                    fingerprint_data: this.fingerprintData,
                    vpn_data: this.vpnResults
                })
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const result = await response.json();
            
            if (!result.success) {
                throw new Error(result.error || 'Неизвестная ошибка сервера');
            }
            
            return result;
            
        } catch (error) {
            console.error('[FraudManager] ❌ Ошибка регистрации отпечатка:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }
    
    /**
     * Проверяет, можно ли выполнить действие (для интеграции с приложением)
     */
    canPerformAction(actionType) {
        if (this.userBlocked) {
            console.log(`[FraudManager] 🚫 Действие "${actionType}" заблокировано - пользователь в черном списке`);
            return false;
        }
        
        if (!this.initialized) {
            console.log(`[FraudManager] ⏳ Действие "${actionType}" разрешено - антифрод система еще инициализируется`);
            return true; // РАЗРЕШАЕМ действия во время инициализации
        }
        
        return true;
    }
    
    /**
     * Логирует подозрительную активность
     */
    async logSuspiciousActivity(activityType, details, initData) {
        try {
            console.log(`[FraudManager] 🚨 Подозрительная активность: ${activityType}`, details);
            
            // Здесь можно добавить отправку на сервер для анализа
            const response = await fetch('/api/fraud-detection.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    action: 'log_suspicious_activity',
                    initData: initData,
                    activity_type: activityType,
                    details: details,
                    timestamp: Date.now(),
                    fingerprint: this.fingerprintData?.fingerprint
                })
            });
            
            if (response.ok) {
                const result = await response.json();
                console.log('[FraudManager] 📝 Подозрительная активность зарегистрирована');
                return result;
            }
            
        } catch (error) {
            console.error('[FraudManager] ❌ Ошибка логирования подозрительной активности:', error);
        }
        
        return null;
    }
    
    /**
     * Получает информацию о текущем состоянии антифрод системы
     */
    getStatus() {
        return {
            initialized: this.initialized,
            userBlocked: this.userBlocked,
            hasFingerprint: !!this.fingerprintData,
            fingerprintId: this.fingerprintData?.fingerprint?.substring(0, 16) + '...' || null,
            blockReason: this.fraudBlocker?.getBlockReason() || null
        };
    }
    
    /**
     * Принудительно проверяет статус фрода (для периодических проверок)
     */
    async recheckFraudStatus(initData) {
        try {
            console.log('[FraudManager] 🔄 Повторная проверка статуса фрода...');
            
            const isBlocked = await this.fraudBlocker.checkFraudStatus(initData);
            
            if (isBlocked && !this.userBlocked) {
                this.userBlocked = true;
                console.log('[FraudManager] 🚫 Пользователь заблокирован при повторной проверке');
                return false;
            }
            
            return !isBlocked;
            
        } catch (error) {
            console.error('[FraudManager] ❌ Ошибка повторной проверки фрода:', error);
            return true; // В случае ошибки не блокируем пользователя
        }
    }
    
    /**
     * Интеграция с системой рекламы - проверка перед показом рекламы
     */
    async validateAdRequest(adType, initData) {
        if (!this.canPerformAction('view_ad')) {
            return {
                allowed: false,
                reason: 'user_blocked'
            };
        }
        
        // Дополнительные проверки для рекламы
        const suspiciousPatterns = this.detectSuspiciousAdBehavior();
        
        if (suspiciousPatterns.length > 0) {
            await this.logSuspiciousActivity('suspicious_ad_behavior', {
                ad_type: adType,
                patterns: suspiciousPatterns
            }, initData);
            
            return {
                allowed: false,
                reason: 'suspicious_behavior',
                patterns: suspiciousPatterns
            };
        }
        
        return {
            allowed: true
        };
    }
    
    /**
     * Детектирует подозрительное поведение при просмотре рекламы
     */
    detectSuspiciousAdBehavior() {
        const patterns = [];
        
        // Проверяем, скрыта ли страница (пользователь переключился на другую вкладку)
        if (document.hidden) {
            patterns.push('page_hidden');
        }
        
        // Проверяем размер окна (слишком маленький может указывать на бота)
        if (window.innerWidth < 300 || window.innerHeight < 300) {
            patterns.push('small_window');
        }
        
        // Проверяем, есть ли фокус на странице
        if (!document.hasFocus()) {
            patterns.push('no_focus');
        }
        
        return patterns;
    }

    /**
     * Проверяет самореферралы и подозрительную реферральную активность
     * @param {string} initData - Данные инициализации Telegram
     * @returns {Promise<Object|null>} Результат проверки самореферралов
     */
    async checkSelfReferrals(initData) {
        try {
            console.log('[FraudManager] 🔍 Проверяем самореферралы...');

            // Проверяем, загружен ли SelfReferralDetector
            if (!window.SelfReferralDetector) {
                console.warn('[FraudManager] ⚠️ SelfReferralDetector не загружен, пропускаем проверку');
                return null;
            }

            // Создаем экземпляр детектора
            const detector = new window.SelfReferralDetector();

            // Анализируем реферральные связи
            const analysis = await detector.analyzeReferralRelations(
                initData,
                this.fingerprintData?.fingerprint
            );

            console.log('[FraudManager] 📊 Результат анализа самореферралов:', {
                should_block: analysis.should_block,
                risk_score: analysis.risk_score,
                violations_count: analysis.violations?.length || 0
            });

            // Возвращаем результат анализа
            return analysis;

        } catch (error) {
            console.error('[FraudManager] ❌ Ошибка проверки самореферралов:', error.message);
            return null;
        }
    }

    /**
     * Fallback инициализация без initData
     */
    async initializeFallback() {
        console.log('[FraudManager] 🔄 Запуск fallback инициализации...');

        try {
            // Помечаем как инициализированный, чтобы не блокировать действия
            this.initialized = true;

            console.log('[FraudManager] ✅ Fallback инициализация завершена - система работает в упрощенном режиме');

        } catch (error) {
            console.error('[FraudManager] ❌ Ошибка fallback инициализации:', error);
            // Все равно помечаем как инициализированный, чтобы не блокировать приложение
            this.initialized = true;
        }
    }
}

// Экспортируем класс в глобальную область видимости
window.FraudManager = FraudManager;

// Функция для создания глобального экземпляра
function createFraudManagerInstance() {
    if (!window.fraudManager) {
        try {
            window.fraudManager = new FraudManager();
            console.log('[FraudManager] 🎯 Глобальный экземпляр создан');
        } catch (error) {
            console.error('[FraudManager] ❌ Ошибка создания экземпляра:', error.message);
        }
    }
    return window.fraudManager;
}

// Создаем глобальный экземпляр с проверкой
createFraudManagerInstance();

// Автоматическая инициализация при загрузке Telegram WebApp
if (window.Telegram && window.Telegram.WebApp) {
    window.Telegram.WebApp.ready(() => {
        console.log('[FraudManager] 📱 Telegram WebApp готов, запускаем антифрод систему...');
        
        // Получаем initData из Telegram
        const initData = window.Telegram.WebApp.initData;
        
        if (initData) {
            // Запускаем инициализацию с небольшой задержкой
            setTimeout(() => {
                window.fraudManager.initialize(initData);
            }, 1000);
        } else {
            console.warn('[FraudManager] ⚠️ initData не найден, запускаем fallback инициализацию');
            // Fallback инициализация без initData
            setTimeout(() => {
                window.fraudManager.initializeFallback();
            }, 1500);
        }
    });
} else {
    console.log('[FraudManager] 🌐 Не Telegram среда, ожидаем ручной инициализации');
}

// Добавляем недостающие методы для тестирования
FraudManager.prototype.checkVPN = async function(initData) {
    try {
        console.log('[FraudManager] 🌐 Проверяем VPN...');

        if (!this.vpnDetector) {
            this.vpnDetector = new window.VPNDetector();
        }

        // Запускаем VPN детекцию
        const vpnResult = await this.vpnDetector.detectVPN();

        return {
            vpn_detected: vpnResult.is_vpn_detected || false,
            risk_score: vpnResult.risk_score || 0,
            should_block: (vpnResult.risk_score || 0) >= 70,
            details: vpnResult
        };

    } catch (error) {
        console.error('[FraudManager] ❌ Ошибка проверки VPN:', error);
        return {
            vpn_detected: false,
            risk_score: 0,
            should_block: false,
            error: error.message
        };
    }
};

FraudManager.prototype.checkSelfReferral = async function(initData) {
    try {
        console.log('[FraudManager] 👥 Проверяем самореферралы...');

        // Используем существующий метод
        return await this.checkSelfReferrals(initData);

    } catch (error) {
        console.error('[FraudManager] ❌ Ошибка проверки самореферралов:', error);
        return {
            self_referral_detected: false,
            risk_score: 0,
            should_block: false,
            error: error.message
        };
    }
};

FraudManager.prototype.performComprehensiveCheck = async function(initData) {
    try {
        console.log('[FraudManager] 🔍 Выполняем комплексную проверку...');

        const results = {
            vpn_check: null,
            referral_check: null,
            overall_risk_score: 0,
            should_block: false,
            block_reasons: []
        };

        // Проверяем VPN
        try {
            results.vpn_check = await this.checkVPN(initData);
            if (results.vpn_check.should_block) {
                results.block_reasons.push('VPN_DETECTED');
            }
            results.overall_risk_score += results.vpn_check.risk_score || 0;
        } catch (error) {
            console.error('[FraudManager] ❌ Ошибка VPN проверки:', error);
        }

        // Проверяем самореферралы
        try {
            results.referral_check = await this.checkSelfReferral(initData);
            if (results.referral_check && results.referral_check.should_block) {
                results.block_reasons.push('SELF_REFERRAL_DETECTED');
            }
            results.overall_risk_score += (results.referral_check && results.referral_check.risk_score) || 0;
        } catch (error) {
            console.error('[FraudManager] ❌ Ошибка проверки рефералов:', error);
        }

        // Определяем финальное решение
        results.should_block = results.overall_risk_score >= 100 || results.block_reasons.length > 0;

        console.log('[FraudManager] 📊 Комплексная проверка завершена:', results);

        return results;

    } catch (error) {
        console.error('[FraudManager] ❌ Ошибка комплексной проверки:', error);
        return {
            vpn_check: null,
            referral_check: null,
            overall_risk_score: 0,
            should_block: false,
            block_reasons: [],
            error: error.message
        };
    }
};

console.log('✅ [FraudManager] Главный менеджер антифрод системы загружен');
