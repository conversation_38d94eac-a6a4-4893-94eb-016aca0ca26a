/**
 * Терминальный тест системы детекции самореферралов
 * Запуск: node test_self_referral_terminal.js
 */

// Подключаем необходимые модули
const fs = require('fs');
const path = require('path');

// Мок для fetch API
global.fetch = async (url, options) => {
    console.log(`[MOCK FETCH] ${options.method} ${url}`);
    console.log('[MOCK FETCH] Body:', options.body);
    
    const body = JSON.parse(options.body);
    
    // Симулируем ответ API в зависимости от action
    switch (body.action) {
        case 'analyze_referrals':
            return {
                ok: true,
                json: async () => ({
                    success: true,
                    data: {
                        should_block: true,
                        risk_score: 75,
                        violations: [
                            {
                                type: 'cyclic_referral',
                                description: 'Обнаружена циклическая реферральная цепочка',
                                details: ['123', '456', '123'],
                                risk_points: 30
                            },
                            {
                                type: 'fingerprint_duplicate',
                                description: 'Обнаружены дублирующиеся отпечатки устройств',
                                details: [
                                    { user_id: '456', created_at: '2024-01-01', ip_address: '***********' }
                                ],
                                risk_points: 25
                            },
                            {
                                type: 'suspicious_ip_pattern',
                                description: 'Подозрительные IP паттерны в реферральной сети',
                                details: { same_ip_count: 5, same_ip_users: ['111', '222', '333', '444'] },
                                risk_points: 20
                            }
                        ],
                        recommendations: [
                            'Заблокировать всех пользователей в циклической цепочке',
                            'Проверить устройства с дублирующимися отпечатками',
                            'Ограничить регистрации с данного IP'
                        ]
                    }
                })
            };
            
        default:
            return {
                ok: false,
                json: async () => ({ success: false, error: 'Unknown action' })
            };
    }
};

// Мок для URLSearchParams
global.URLSearchParams = class URLSearchParams {
    constructor(data) {
        this.data = data;
    }
    
    get(key) {
        if (key === 'user') {
            return encodeURIComponent(JSON.stringify({ id: '123456789' }));
        }
        return null;
    }
};

// Мок для decodeURIComponent (уже есть в Node.js)

// Загружаем SelfReferralDetector
const selfReferralCode = fs.readFileSync(path.join(__dirname, 'js', 'self-referral-detector.js'), 'utf8');

// Создаем контекст для выполнения кода
const vm = require('vm');
const context = {
    console,
    fetch: global.fetch,
    URLSearchParams: global.URLSearchParams,
    decodeURIComponent,
    encodeURIComponent,
    JSON,
    Error,
    window: {}
};

// Выполняем код SelfReferralDetector в контексте
vm.createContext(context);
vm.runInContext(selfReferralCode, context);

// Получаем класс из контекста
const SelfReferralDetector = context.window.SelfReferralDetector;

async function runSelfReferralTests() {
    console.log('🧪 ЗАПУСК ТЕСТОВ СИСТЕМЫ ДЕТЕКЦИИ САМОРЕФЕРРАЛОВ');
    console.log('=' .repeat(60));
    
    try {
        // Создаем экземпляр детектора
        const detector = new SelfReferralDetector();
        
        // Тестовые данные
        const testInitData = 'user=%7B%22id%22%3A123456789%7D&other_param=value';
        const testFingerprint = 'test_fingerprint_hash_12345';
        
        console.log('\n📋 ТЕСТОВЫЕ ДАННЫЕ:');
        console.log('- User ID: 123456789');
        console.log('- Fingerprint:', testFingerprint);
        console.log('- InitData:', testInitData);
        
        console.log('\n🔍 ТЕСТ 1: Анализ реферральных связей');
        console.log('-'.repeat(40));
        
        const analysis = await detector.analyzeReferralRelations(testInitData, testFingerprint);
        
        console.log('\n📊 РЕЗУЛЬТАТЫ АНАЛИЗА:');
        console.log('- Should Block:', analysis.should_block ? '✅ ДА' : '❌ НЕТ');
        console.log('- Risk Score:', analysis.risk_score);
        console.log('- Is Suspicious:', analysis.is_suspicious ? '⚠️ ДА' : '✅ НЕТ');
        console.log('- Violations Count:', analysis.violations.length);
        
        if (analysis.violations.length > 0) {
            console.log('\n🚨 ОБНАРУЖЕННЫЕ НАРУШЕНИЯ:');
            analysis.violations.forEach((violation, index) => {
                console.log(`${index + 1}. ${violation.type}: ${violation.description} (${violation.risk_points} баллов)`);
            });
        }
        
        if (analysis.recommendations && analysis.recommendations.length > 0) {
            console.log('\n💡 РЕКОМЕНДАЦИИ:');
            analysis.recommendations.forEach((rec, index) => {
                console.log(`${index + 1}. ${rec}`);
            });
        }
        
        console.log('\n🔍 ТЕСТ 2: Извлечение User ID из InitData');
        console.log('-'.repeat(40));
        
        const extractedUserId = detector.extractUserIdFromInitData(testInitData);
        console.log('- Извлеченный User ID:', extractedUserId);
        console.log('- Корректность:', extractedUserId === '123456789' ? '✅ КОРРЕКТНО' : '❌ ОШИБКА');
        
        // Проверяем результаты
        console.log('\n🎯 ИТОГОВАЯ ПРОВЕРКА:');
        console.log('-'.repeat(40));
        
        const tests = [
            {
                name: 'API запрос выполнен',
                passed: analysis.should_block !== undefined
            },
            {
                name: 'Risk score получен',
                passed: typeof analysis.risk_score === 'number'
            },
            {
                name: 'Нарушения обнаружены',
                passed: analysis.violations && analysis.violations.length > 0
            },
            {
                name: 'Блокировка рекомендована',
                passed: analysis.should_block === true
            },
            {
                name: 'User ID извлечен корректно',
                passed: extractedUserId === '123456789'
            }
        ];
        
        let passedTests = 0;
        tests.forEach(test => {
            const status = test.passed ? '✅' : '❌';
            console.log(`${status} ${test.name}`);
            if (test.passed) passedTests++;
        });
        
        console.log('\n📈 РЕЗУЛЬТАТ ТЕСТИРОВАНИЯ:');
        console.log(`Пройдено тестов: ${passedTests}/${tests.length}`);
        
        if (passedTests === tests.length) {
            console.log('🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ УСПЕШНО!');
            console.log('✅ Система детекции самореферралов работает корректно');
        } else {
            console.log('⚠️ НЕКОТОРЫЕ ТЕСТЫ НЕ ПРОЙДЕНЫ');
            console.log('❌ Требуется дополнительная отладка');
        }
        
    } catch (error) {
        console.error('\n❌ ОШИБКА ТЕСТИРОВАНИЯ:', error.message);
        console.error('Stack trace:', error.stack);
    }
    
    console.log('\n' + '='.repeat(60));
    console.log('🏁 ТЕСТИРОВАНИЕ ЗАВЕРШЕНО');
}

// Запускаем тесты
runSelfReferralTests().catch(console.error);
