[07-Jul-2025 11:56:17 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 11:56:17 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 11:56:20 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 11:56:20 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 11:56:40 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 11:56:40 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 11:56:44 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 11:56:44 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 11:57:03 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 11:57:04 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 11:57:19 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 11:57:19 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 11:57:56 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 11:57:56 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 11:58:09 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 11:58:09 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 11:58:40 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 11:58:40 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 11:59:30 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 11:59:30 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 11:59:32 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 11:59:32 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 12:08:47 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 12:08:47 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 12:08:50 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 12:08:50 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 12:08:56 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 12:08:57 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 12:08:57 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 12:08:59 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 12:09:02 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 12:09:02 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 12:09:04 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 12:09:08 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 12:09:10 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 12:09:10 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 12:09:15 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 12:09:19 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 12:09:19 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 12:19:10 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 12:19:10 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 12:19:10 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 12:19:15 Europe/Moscow] vpn-detection INFO: Получен запрос на анализ VPN
[07-Jul-2025 12:19:23 Europe/Moscow] VPN Risk Score Calculation: Score=5, Details=private_ip, geo_mismatch
[07-Jul-2025 12:19:23 Europe/Moscow] analyzeVPN DEBUG: Calculated Risk Score: 5
[07-Jul-2025 12:19:23 Europe/Moscow] analyzeVPN DEBUG: Risk Details: private_ip, geo_mismatch
[07-Jul-2025 12:19:23 Europe/Moscow] analyzeVPN DEBUG: VPN Detected: NO
[07-Jul-2025 12:20:11 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 12:20:11 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 12:20:11 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 12:20:30 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 12:20:30 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 12:20:30 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 12:20:37 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 12:20:37 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 12:20:37 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 12:20:37 Europe/Moscow] DEBUG logAdClick: Получен JSON: {"initData":"user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test","adType":"rewarded_video","clickType":"button_click","timestamp":1751880037872,"sessionId":"unknown"}
[07-Jul-2025 12:20:37 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 12:20:37 Europe/Moscow] DEBUG logAdClick: Декодированные данные: Array
(
    [initData] => user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test
    [adType] => rewarded_video
    [clickType] => button_click
    [timestamp] => 1751880037872
    [sessionId] => unknown
)

[07-Jul-2025 12:20:37 Europe/Moscow] DEBUG logAdClick: Получен JSON: {"initData":"user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test","adType":"rewarded_video","clickType":"ad_request","timestamp":1751880037873,"sessionId":"unknown","reason":"User requested ad show"}
[07-Jul-2025 12:20:37 Europe/Moscow] logAdClick DEBUG: Начинаем валидацию initData: user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_i...
[07-Jul-2025 12:20:37 Europe/Moscow] DEBUG logAdClick: Декодированные данные: Array
(
    [initData] => user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test
    [adType] => rewarded_video
    [clickType] => ad_request
    [timestamp] => 1751880037873
    [sessionId] => unknown
    [reason] => User requested ad show
)

[07-Jul-2025 12:20:37 Europe/Moscow] validateTelegramInitData: auth_date старый (но разрешен).
[07-Jul-2025 12:20:37 Europe/Moscow] logAdClick DEBUG: Начинаем валидацию initData: user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_i...
[07-Jul-2025 12:20:37 Europe/Moscow] validateTelegramInitData: auth_date старый (но разрешен).
[07-Jul-2025 12:20:37 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[07-Jul-2025 12:20:37 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[07-Jul-2025 12:20:37 Europe/Moscow] logAdClick DEBUG: Результат validateTelegramInitData: FALSE
[07-Jul-2025 12:20:37 Europe/Moscow] logAdClick DEBUG: Результат validateTelegramInitData: FALSE
[07-Jul-2025 12:20:37 Europe/Moscow] logAdClick WARNING: Стандартная валидация не прошла, пробуем упрощенную
[07-Jul-2025 12:20:37 Europe/Moscow] logAdClick WARNING: Стандартная валидация не прошла, пробуем упрощенную
[07-Jul-2025 12:20:37 Europe/Moscow] logAdClick INFO: Упрощенная валидация прошла для пользователя 12345
[07-Jul-2025 12:20:37 Europe/Moscow] logAdClick INFO: Упрощенная валидация прошла для пользователя 12345
[07-Jul-2025 12:20:37 Europe/Moscow] logAdClick INFO: initData валидирован для пользователя 12345
[07-Jul-2025 12:20:37 Europe/Moscow] logAdClick INFO: initData валидирован для пользователя 12345
[07-Jul-2025 12:20:47 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 12:20:47 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 12:20:48 Europe/Moscow] DEBUG logAdClick: Получен JSON: {"initData":"user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test","adType":"rewarded_video","clickType":"ad_error","timestamp":1751880047433,"sessionId":"unknown","reason":"Failed to load script: https://us.convers.link/users/info?callback=userinfo_rp_pu"}
[07-Jul-2025 12:20:48 Europe/Moscow] DEBUG logAdClick: Декодированные данные: Array
(
    [initData] => user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test
    [adType] => rewarded_video
    [clickType] => ad_error
    [timestamp] => 1751880047433
    [sessionId] => unknown
    [reason] => Failed to load script: https://us.convers.link/users/info?callback=userinfo_rp_pu
)

[07-Jul-2025 12:20:48 Europe/Moscow] logAdClick DEBUG: Начинаем валидацию initData: user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_i...
[07-Jul-2025 12:20:48 Europe/Moscow] validateTelegramInitData: auth_date старый (но разрешен).
[07-Jul-2025 12:20:48 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[07-Jul-2025 12:20:48 Europe/Moscow] logAdClick DEBUG: Результат validateTelegramInitData: FALSE
[07-Jul-2025 12:20:48 Europe/Moscow] logAdClick WARNING: Стандартная валидация не прошла, пробуем упрощенную
[07-Jul-2025 12:20:48 Europe/Moscow] logAdClick INFO: Упрощенная валидация прошла для пользователя 12345
[07-Jul-2025 12:20:48 Europe/Moscow] logAdClick INFO: initData валидирован для пользователя 12345
[07-Jul-2025 12:21:01 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 12:21:23 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 12:21:23 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 12:21:23 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 12:21:54 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 12:22:24 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 12:22:45 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 12:22:45 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 12:22:45 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 12:23:05 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 12:23:05 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 12:23:05 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 12:23:36 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 12:23:37 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 12:23:37 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 12:23:39 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 12:23:42 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 12:23:42 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 12:24:06 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 12:24:36 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 12:25:06 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 12:25:36 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 12:26:06 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 12:26:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 12:26:56 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 12:26:56 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 12:27:28 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 12:27:28 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 12:27:33 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 12:27:38 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 12:27:38 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 12:27:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 12:28:06 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 12:28:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 12:29:21 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 12:29:25 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 12:29:25 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 12:29:27 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 12:29:27 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 12:29:40 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 12:29:43 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 12:29:43 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 12:29:45 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 12:29:45 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 12:29:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 12:30:14 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 12:30:14 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 12:30:16 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 12:30:16 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 12:30:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 12:31:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 12:32:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 12:33:06 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 12:34:33 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 12:34:33 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 12:34:35 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 12:34:35 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 12:34:40 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 12:35:10 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 12:35:17 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 12:35:17 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 12:36:08 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 12:36:08 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 12:36:10 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 12:36:10 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 12:36:48 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 12:38:45 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 12:38:45 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 12:38:47 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 12:38:47 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 12:38:56 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 12:44:46 Europe/Moscow] PHP Fatal error:  Cannot redeclare analyzeVPN() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\fraud-detection.php:1733) in D:\OSPanel\domains\argun-defolt.loc\api\vpn-detection.php on line 55
[07-Jul-2025 12:44:46 Europe/Moscow] PHP Fatal error:  Cannot redeclare analyzeVPN() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\fraud-detection.php:1733) in D:\OSPanel\domains\argun-defolt.loc\api\vpn-detection.php on line 55
[07-Jul-2025 12:44:48 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 12:44:48 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 12:44:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare analyzeVPN() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\fraud-detection.php:1733) in D:\OSPanel\domains\argun-defolt.loc\api\vpn-detection.php on line 55
[07-Jul-2025 12:44:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare analyzeVPN() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\fraud-detection.php:1733) in D:\OSPanel\domains\argun-defolt.loc\api\vpn-detection.php on line 55
[07-Jul-2025 12:52:22 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 12:52:22 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 12:52:26 Europe/Moscow] PHP Fatal error:  Cannot redeclare analyzeVPN() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\fraud-detection.php:1733) in D:\OSPanel\domains\argun-defolt.loc\api\vpn-detection.php on line 55
[07-Jul-2025 12:52:27 Europe/Moscow] PHP Fatal error:  Cannot redeclare analyzeVPN() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\fraud-detection.php:1733) in D:\OSPanel\domains\argun-defolt.loc\api\vpn-detection.php on line 55
[07-Jul-2025 12:52:27 Europe/Moscow] PHP Fatal error:  Cannot redeclare analyzeVPN() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\fraud-detection.php:1733) in D:\OSPanel\domains\argun-defolt.loc\api\vpn-detection.php on line 55
[07-Jul-2025 12:52:33 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 12:52:33 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 12:52:34 Europe/Moscow] PHP Fatal error:  Cannot redeclare analyzeVPN() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\fraud-detection.php:1733) in D:\OSPanel\domains\argun-defolt.loc\api\vpn-detection.php on line 55
[07-Jul-2025 12:52:34 Europe/Moscow] PHP Fatal error:  Cannot redeclare analyzeVPN() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\fraud-detection.php:1733) in D:\OSPanel\domains\argun-defolt.loc\api\vpn-detection.php on line 55
[07-Jul-2025 12:52:38 Europe/Moscow] PHP Fatal error:  Cannot redeclare analyzeVPN() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\fraud-detection.php:1733) in D:\OSPanel\domains\argun-defolt.loc\api\vpn-detection.php on line 55
[07-Jul-2025 12:54:40 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 12:54:40 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 12:54:42 Europe/Moscow] PHP Fatal error:  Cannot redeclare analyzeVPN() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\fraud-detection.php:1733) in D:\OSPanel\domains\argun-defolt.loc\api\vpn-detection.php on line 55
[07-Jul-2025 12:54:42 Europe/Moscow] PHP Fatal error:  Cannot redeclare analyzeVPN() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\fraud-detection.php:1733) in D:\OSPanel\domains\argun-defolt.loc\api\vpn-detection.php on line 55
[07-Jul-2025 12:54:50 Europe/Moscow] PHP Fatal error:  Cannot redeclare analyzeVPN() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\fraud-detection.php:1733) in D:\OSPanel\domains\argun-defolt.loc\api\vpn-detection.php on line 55
[07-Jul-2025 12:54:52 Europe/Moscow] PHP Fatal error:  Cannot redeclare analyzeVPN() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\fraud-detection.php:1733) in D:\OSPanel\domains\argun-defolt.loc\api\vpn-detection.php on line 55
[07-Jul-2025 12:54:52 Europe/Moscow] PHP Fatal error:  Cannot redeclare analyzeVPN() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\fraud-detection.php:1733) in D:\OSPanel\domains\argun-defolt.loc\api\vpn-detection.php on line 55
[07-Jul-2025 12:54:55 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 12:54:55 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 12:55:09 Europe/Moscow] PHP Fatal error:  Cannot redeclare analyzeVPN() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\fraud-detection.php:1733) in D:\OSPanel\domains\argun-defolt.loc\api\vpn-detection.php on line 55
[07-Jul-2025 12:55:09 Europe/Moscow] PHP Fatal error:  Cannot redeclare analyzeVPN() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\fraud-detection.php:1733) in D:\OSPanel\domains\argun-defolt.loc\api\vpn-detection.php on line 55
[07-Jul-2025 12:56:31 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 12:56:31 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 12:56:33 Europe/Moscow] PHP Fatal error:  Cannot redeclare analyzeVPN() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\fraud-detection.php:1733) in D:\OSPanel\domains\argun-defolt.loc\api\vpn-detection.php on line 55
[07-Jul-2025 12:56:33 Europe/Moscow] PHP Fatal error:  Cannot redeclare analyzeVPN() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\fraud-detection.php:1733) in D:\OSPanel\domains\argun-defolt.loc\api\vpn-detection.php on line 55
[07-Jul-2025 12:56:41 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 12:56:41 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 12:56:41 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 12:56:41 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 12:56:41 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 12:56:41 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 12:57:23 Europe/Moscow] PHP Fatal error:  Cannot redeclare analyzeVPN() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\fraud-detection.php:1733) in D:\OSPanel\domains\argun-defolt.loc\api\vpn-detection.php on line 55
[07-Jul-2025 12:57:23 Europe/Moscow] PHP Fatal error:  Cannot redeclare analyzeVPN() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\fraud-detection.php:1733) in D:\OSPanel\domains\argun-defolt.loc\api\vpn-detection.php on line 55
[07-Jul-2025 12:57:30 Europe/Moscow] PHP Fatal error:  Cannot redeclare analyzeVPN() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\fraud-detection.php:1733) in D:\OSPanel\domains\argun-defolt.loc\api\vpn-detection.php on line 55
[07-Jul-2025 12:57:33 Europe/Moscow] PHP Fatal error:  Cannot redeclare analyzeVPN() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\fraud-detection.php:1733) in D:\OSPanel\domains\argun-defolt.loc\api\vpn-detection.php on line 55
[07-Jul-2025 12:57:37 Europe/Moscow] PHP Fatal error:  Cannot redeclare analyzeVPN() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\fraud-detection.php:1733) in D:\OSPanel\domains\argun-defolt.loc\api\vpn-detection.php on line 55
[07-Jul-2025 12:57:45 Europe/Moscow] PHP Fatal error:  Cannot redeclare analyzeVPN() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\fraud-detection.php:1733) in D:\OSPanel\domains\argun-defolt.loc\api\vpn-detection.php on line 55
[07-Jul-2025 12:57:48 Europe/Moscow] PHP Fatal error:  Cannot redeclare analyzeVPN() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\fraud-detection.php:1733) in D:\OSPanel\domains\argun-defolt.loc\api\vpn-detection.php on line 55
[07-Jul-2025 12:57:48 Europe/Moscow] PHP Fatal error:  Cannot redeclare analyzeVPN() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\fraud-detection.php:1733) in D:\OSPanel\domains\argun-defolt.loc\api\vpn-detection.php on line 55
[07-Jul-2025 12:57:50 Europe/Moscow] PHP Fatal error:  Cannot redeclare analyzeVPN() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\fraud-detection.php:1733) in D:\OSPanel\domains\argun-defolt.loc\api\vpn-detection.php on line 55
[07-Jul-2025 12:57:54 Europe/Moscow] PHP Fatal error:  Cannot redeclare analyzeVPN() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\fraud-detection.php:1733) in D:\OSPanel\domains\argun-defolt.loc\api\vpn-detection.php on line 55
[07-Jul-2025 12:57:55 Europe/Moscow] PHP Fatal error:  Cannot redeclare analyzeVPN() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\fraud-detection.php:1733) in D:\OSPanel\domains\argun-defolt.loc\api\vpn-detection.php on line 55
[07-Jul-2025 12:57:57 Europe/Moscow] PHP Fatal error:  Cannot redeclare analyzeVPN() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\fraud-detection.php:1733) in D:\OSPanel\domains\argun-defolt.loc\api\vpn-detection.php on line 55
[07-Jul-2025 12:57:57 Europe/Moscow] PHP Fatal error:  Cannot redeclare analyzeVPN() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\fraud-detection.php:1733) in D:\OSPanel\domains\argun-defolt.loc\api\vpn-detection.php on line 55
[07-Jul-2025 12:57:59 Europe/Moscow] PHP Fatal error:  Cannot redeclare analyzeVPN() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\fraud-detection.php:1733) in D:\OSPanel\domains\argun-defolt.loc\api\vpn-detection.php on line 55
[07-Jul-2025 12:57:59 Europe/Moscow] PHP Fatal error:  Cannot redeclare analyzeVPN() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\fraud-detection.php:1733) in D:\OSPanel\domains\argun-defolt.loc\api\vpn-detection.php on line 55
[07-Jul-2025 12:58:23 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 12:58:24 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 12:58:27 Europe/Moscow] PHP Fatal error:  Cannot redeclare analyzeVPN() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\fraud-detection.php:1733) in D:\OSPanel\domains\argun-defolt.loc\api\vpn-detection.php on line 55
[07-Jul-2025 12:58:27 Europe/Moscow] PHP Fatal error:  Cannot redeclare analyzeVPN() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\fraud-detection.php:1733) in D:\OSPanel\domains\argun-defolt.loc\api\vpn-detection.php on line 55
[07-Jul-2025 12:58:35 Europe/Moscow] PHP Fatal error:  Cannot redeclare analyzeVPN() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\fraud-detection.php:1733) in D:\OSPanel\domains\argun-defolt.loc\api\vpn-detection.php on line 55
[07-Jul-2025 12:58:36 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 12:58:36 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 12:58:36 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 12:58:36 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 12:58:36 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 12:58:36 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 12:58:38 Europe/Moscow] PHP Fatal error:  Cannot redeclare analyzeVPN() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\fraud-detection.php:1733) in D:\OSPanel\domains\argun-defolt.loc\api\vpn-detection.php on line 55
[07-Jul-2025 12:58:38 Europe/Moscow] PHP Fatal error:  Cannot redeclare analyzeVPN() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\fraud-detection.php:1733) in D:\OSPanel\domains\argun-defolt.loc\api\vpn-detection.php on line 55
[07-Jul-2025 12:59:18 Europe/Moscow] PHP Fatal error:  Cannot redeclare analyzeVPN() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\fraud-detection.php:1733) in D:\OSPanel\domains\argun-defolt.loc\api\vpn-detection.php on line 55
[07-Jul-2025 12:59:20 Europe/Moscow] PHP Fatal error:  Cannot redeclare analyzeVPN() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\fraud-detection.php:1733) in D:\OSPanel\domains\argun-defolt.loc\api\vpn-detection.php on line 55
[07-Jul-2025 12:59:20 Europe/Moscow] PHP Fatal error:  Cannot redeclare analyzeVPN() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\fraud-detection.php:1733) in D:\OSPanel\domains\argun-defolt.loc\api\vpn-detection.php on line 55
[07-Jul-2025 13:00:27 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 13:00:27 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 13:00:29 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:00:29 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:02:42 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 13:02:42 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 13:02:43 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:02:43 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:02:48 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:02:52 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:02:55 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:02:55 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:03:50 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 13:03:50 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 13:04:02 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:04:10 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:04:36 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 13:04:36 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 13:04:52 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 13:04:52 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 13:11:27 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 13:11:27 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 13:11:41 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 13:11:41 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 13:11:41 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 13:11:41 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 13:11:41 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 13:11:41 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 13:12:34 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 13:12:34 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 13:12:34 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 13:12:34 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 13:12:34 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 13:12:34 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 13:19:23 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 13:19:23 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 13:19:38 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 13:19:38 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 13:19:54 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:19:54 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:19:54 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 13:20:08 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:20:08 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:20:08 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 13:20:20 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:20:20 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:20:20 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 13:20:27 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 13:20:27 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 13:20:27 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 13:20:27 Europe/Moscow] DEBUG logAdClick: Получен JSON: {"initData":"user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test","adType":"interstitial","clickType":"ad_request","timestamp":1751883627600,"sessionId":"unknown","reason":"User requested ad show"}
[07-Jul-2025 13:20:27 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 13:20:27 Europe/Moscow] DEBUG logAdClick: Декодированные данные: Array
(
    [initData] => user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test
    [adType] => interstitial
    [clickType] => ad_request
    [timestamp] => 1751883627600
    [sessionId] => unknown
    [reason] => User requested ad show
)

[07-Jul-2025 13:20:27 Europe/Moscow] DEBUG logAdClick: Получен JSON: {"initData":"user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test","adType":"interstitial","clickType":"button_click","timestamp":1751883627598,"sessionId":"unknown"}
[07-Jul-2025 13:20:27 Europe/Moscow] logAdClick DEBUG: Начинаем валидацию initData: user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_i...
[07-Jul-2025 13:20:27 Europe/Moscow] DEBUG logAdClick: Декодированные данные: Array
(
    [initData] => user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test
    [adType] => interstitial
    [clickType] => button_click
    [timestamp] => 1751883627598
    [sessionId] => unknown
)

[07-Jul-2025 13:20:27 Europe/Moscow] validateTelegramInitData: auth_date старый (но разрешен).
[07-Jul-2025 13:20:27 Europe/Moscow] logAdClick DEBUG: Начинаем валидацию initData: user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_i...
[07-Jul-2025 13:20:27 Europe/Moscow] validateTelegramInitData: auth_date старый (но разрешен).
[07-Jul-2025 13:20:27 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[07-Jul-2025 13:20:27 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[07-Jul-2025 13:20:27 Europe/Moscow] logAdClick DEBUG: Результат validateTelegramInitData: FALSE
[07-Jul-2025 13:20:27 Europe/Moscow] logAdClick DEBUG: Результат validateTelegramInitData: FALSE
[07-Jul-2025 13:20:27 Europe/Moscow] logAdClick WARNING: Стандартная валидация не прошла, пробуем упрощенную
[07-Jul-2025 13:20:27 Europe/Moscow] logAdClick INFO: Упрощенная валидация прошла для пользователя 12345
[07-Jul-2025 13:20:27 Europe/Moscow] logAdClick WARNING: Стандартная валидация не прошла, пробуем упрощенную
[07-Jul-2025 13:20:27 Europe/Moscow] logAdClick INFO: initData валидирован для пользователя 12345
[07-Jul-2025 13:20:27 Europe/Moscow] logAdClick INFO: Упрощенная валидация прошла для пользователя 12345
[07-Jul-2025 13:20:27 Europe/Moscow] logAdClick INFO: initData валидирован для пользователя 12345
[07-Jul-2025 13:20:30 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 13:20:30 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 13:20:30 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 13:20:30 Europe/Moscow] DEBUG logAdClick: Получен JSON: {"initData":"user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test","adType":"interstitial","clickType":"ad_error","timestamp":1751883630218,"sessionId":"unknown","reason":"Cannot read properties of null (reading 'length')"}
[07-Jul-2025 13:20:30 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 13:20:30 Europe/Moscow] DEBUG logAdClick: Декодированные данные: Array
(
    [initData] => user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test
    [adType] => interstitial
    [clickType] => ad_error
    [timestamp] => 1751883630218
    [sessionId] => unknown
    [reason] => Cannot read properties of null (reading 'length')
)

[07-Jul-2025 13:20:30 Europe/Moscow] DEBUG logAdClick: Получен JSON: {"initData":"user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test","adType":"interstitial","clickType":"no_ads_available","timestamp":1751883630219,"sessionId":"unknown","reason":"No ads available from ad network"}
[07-Jul-2025 13:20:30 Europe/Moscow] logAdClick DEBUG: Начинаем валидацию initData: user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_i...
[07-Jul-2025 13:20:30 Europe/Moscow] validateTelegramInitData: auth_date старый (но разрешен).
[07-Jul-2025 13:20:30 Europe/Moscow] DEBUG logAdClick: Декодированные данные: Array
(
    [initData] => user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test
    [adType] => interstitial
    [clickType] => no_ads_available
    [timestamp] => 1751883630219
    [sessionId] => unknown
    [reason] => No ads available from ad network
)

[07-Jul-2025 13:20:30 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[07-Jul-2025 13:20:30 Europe/Moscow] logAdClick DEBUG: Результат validateTelegramInitData: FALSE
[07-Jul-2025 13:20:30 Europe/Moscow] logAdClick DEBUG: Начинаем валидацию initData: user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_i...
[07-Jul-2025 13:20:30 Europe/Moscow] logAdClick WARNING: Стандартная валидация не прошла, пробуем упрощенную
[07-Jul-2025 13:20:30 Europe/Moscow] logAdClick INFO: Упрощенная валидация прошла для пользователя 12345
[07-Jul-2025 13:20:30 Europe/Moscow] validateTelegramInitData: auth_date старый (но разрешен).
[07-Jul-2025 13:20:30 Europe/Moscow] logAdClick INFO: initData валидирован для пользователя 12345
[07-Jul-2025 13:20:30 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[07-Jul-2025 13:20:30 Europe/Moscow] logAdClick DEBUG: Результат validateTelegramInitData: FALSE
[07-Jul-2025 13:20:30 Europe/Moscow] logAdClick WARNING: Стандартная валидация не прошла, пробуем упрощенную
[07-Jul-2025 13:20:30 Europe/Moscow] logAdClick INFO: Упрощенная валидация прошла для пользователя 12345
[07-Jul-2025 13:20:30 Europe/Moscow] logAdClick INFO: initData валидирован для пользователя 12345
[07-Jul-2025 13:20:37 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 13:20:37 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 13:20:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:20:58 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 13:20:58 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 13:20:58 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 13:20:58 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 13:20:58 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 13:20:58 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 13:20:59 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:20:59 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:20:59 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 13:21:30 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:22:00 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:22:30 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:23:00 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:23:30 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:24:00 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:24:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:25:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:26:00 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:26:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:27:55 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:28:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:29:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:30:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:31:00 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:31:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:32:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:48:08 Europe/Moscow] vpn-detection INFO: Получен запрос на анализ VPN
[07-Jul-2025 13:48:14 Europe/Moscow] PHP Warning:  file_get_contents(http://ip-api.com/json/127.0.0.1?fields=status,message,country,countryCode,region,regionName,city,zip,lat,lon,timezone,isp,org,as,proxy,hosting,query): Failed to open stream: Попытка установить соединение была безуспешной, т.к. от другого компьютера за требуемое время не получен нужный отклик, или было разорвано уже установленное соединение из-за неверного отклика уже подключенного компьютера in D:\OSPanel\domains\argun-defolt.loc\api\vpn-detection.php on line 293
[07-Jul-2025 13:48:14 Europe/Moscow] VPN Risk Score Calculation: Score=5, Details=private_ip, geo_mismatch
[07-Jul-2025 13:48:14 Europe/Moscow] analyzeVPN DEBUG: Calculated Risk Score: 5
[07-Jul-2025 13:48:14 Europe/Moscow] analyzeVPN DEBUG: Risk Details: private_ip, geo_mismatch
[07-Jul-2025 13:48:14 Europe/Moscow] analyzeVPN DEBUG: VPN Detected: NO
[07-Jul-2025 13:48:31 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:49:06 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:49:07 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:49:07 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:49:07 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 13:49:36 Europe/Moscow] vpn-detection INFO: Получен запрос на анализ VPN
[07-Jul-2025 13:49:36 Europe/Moscow] VPN Risk Score Calculation: Score=5, Details=private_ip, geo_mismatch
[07-Jul-2025 13:49:36 Europe/Moscow] analyzeVPN DEBUG: Calculated Risk Score: 5
[07-Jul-2025 13:49:36 Europe/Moscow] analyzeVPN DEBUG: Risk Details: private_ip, geo_mismatch
[07-Jul-2025 13:49:36 Europe/Moscow] analyzeVPN DEBUG: VPN Detected: NO
[07-Jul-2025 13:49:37 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:50:01 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 13:50:01 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 13:50:07 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:50:12 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 13:50:12 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 13:50:12 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 13:50:12 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 13:50:12 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 13:50:12 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 13:50:37 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:50:45 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:50:49 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:50:58 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:51:07 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:51:14 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:51:22 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:51:29 Europe/Moscow] validateTelegramInitData: Отсутствует hash, auth_date или user.
[07-Jul-2025 13:51:29 Europe/Moscow] check-block-status ERROR: Неверные данные авторизации
[07-Jul-2025 13:51:37 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:51:37 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:51:41 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:51:54 Europe/Moscow] validateTelegramInitData: Отсутствует hash, auth_date или user.
[07-Jul-2025 13:51:54 Europe/Moscow] check-block-status ERROR: Неверные данные авторизации
[07-Jul-2025 13:52:07 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:52:37 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:53:07 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:53:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:54:07 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:54:38 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:54:43 Europe/Moscow] validateTelegramInitData: Отсутствует hash, auth_date или user.
[07-Jul-2025 13:54:43 Europe/Moscow] check-block-status ERROR: Неверные данные авторизации
[07-Jul-2025 13:54:43 Europe/Moscow] check-block-status ERROR: Отсутствует initData
[07-Jul-2025 13:54:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:54:58 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:55:03 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:55:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:56:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:57:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:58:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:59:07 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 13:59:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 14:00:07 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 14:00:17 Europe/Moscow] validateTelegramInitData: Отсутствует hash, auth_date или user.
[07-Jul-2025 14:00:17 Europe/Moscow] check-block-status ERROR: Неверные данные авторизации
[07-Jul-2025 14:00:17 Europe/Moscow] check-block-status ERROR: Отсутствует initData
[07-Jul-2025 14:00:20 Europe/Moscow] validateTelegramInitData: Отсутствует hash, auth_date или user.
[07-Jul-2025 14:00:20 Europe/Moscow] check-block-status ERROR: Неверные данные авторизации
[07-Jul-2025 14:00:23 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 14:00:32 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 14:00:37 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 14:01:07 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 14:01:37 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 14:02:07 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 14:02:37 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 14:03:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 14:04:07 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 14:04:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 14:05:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 14:06:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 14:07:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 14:07:59 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 14:07:59 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 14:07:59 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 14:08:12 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 14:08:12 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 14:08:12 Europe/Moscow] DEBUG logAdClick: Получен JSON: {"initData":"user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test","adType":"interstitial","clickType":"button_click","timestamp":1751886492094,"sessionId":"unknown"}
[07-Jul-2025 14:08:12 Europe/Moscow] DEBUG logAdClick: Декодированные данные: Array
(
    [initData] => user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test
    [adType] => interstitial
    [clickType] => button_click
    [timestamp] => 1751886492094
    [sessionId] => unknown
)

[07-Jul-2025 14:08:12 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 14:08:12 Europe/Moscow] logAdClick DEBUG: Начинаем валидацию initData: user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_i...
[07-Jul-2025 14:08:12 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 14:08:12 Europe/Moscow] validateTelegramInitData: auth_date старый (но разрешен).
[07-Jul-2025 14:08:12 Europe/Moscow] DEBUG logAdClick: Получен JSON: {"initData":"user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test","adType":"interstitial","clickType":"ad_request","timestamp":1751886492095,"sessionId":"unknown","reason":"User requested ad show"}
[07-Jul-2025 14:08:12 Europe/Moscow] DEBUG logAdClick: Декодированные данные: Array
(
    [initData] => user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test
    [adType] => interstitial
    [clickType] => ad_request
    [timestamp] => 1751886492095
    [sessionId] => unknown
    [reason] => User requested ad show
)

[07-Jul-2025 14:08:12 Europe/Moscow] logAdClick DEBUG: Начинаем валидацию initData: user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_i...
[07-Jul-2025 14:08:12 Europe/Moscow] validateTelegramInitData: auth_date старый (но разрешен).
[07-Jul-2025 14:08:12 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[07-Jul-2025 14:08:12 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[07-Jul-2025 14:08:12 Europe/Moscow] logAdClick DEBUG: Результат validateTelegramInitData: FALSE
[07-Jul-2025 14:08:12 Europe/Moscow] logAdClick DEBUG: Результат validateTelegramInitData: FALSE
[07-Jul-2025 14:08:12 Europe/Moscow] logAdClick WARNING: Стандартная валидация не прошла, пробуем упрощенную
[07-Jul-2025 14:08:12 Europe/Moscow] logAdClick WARNING: Стандартная валидация не прошла, пробуем упрощенную
[07-Jul-2025 14:08:12 Europe/Moscow] logAdClick INFO: Упрощенная валидация прошла для пользователя 12345
[07-Jul-2025 14:08:12 Europe/Moscow] logAdClick INFO: initData валидирован для пользователя 12345
[07-Jul-2025 14:08:12 Europe/Moscow] logAdClick INFO: Упрощенная валидация прошла для пользователя 12345
[07-Jul-2025 14:08:12 Europe/Moscow] logAdClick INFO: initData валидирован для пользователя 12345
[07-Jul-2025 14:08:22 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 14:08:22 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 14:08:22 Europe/Moscow] DEBUG logAdClick: Получен JSON: {"initData":"user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test","adType":"interstitial","clickType":"ad_error","timestamp":1751886502152,"sessionId":"unknown","reason":"Failed to load script: https://us.convers.link/users/info?callback=userinfo_rp_pu"}
[07-Jul-2025 14:08:22 Europe/Moscow] DEBUG logAdClick: Декодированные данные: Array
(
    [initData] => user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test
    [adType] => interstitial
    [clickType] => ad_error
    [timestamp] => 1751886502152
    [sessionId] => unknown
    [reason] => Failed to load script: https://us.convers.link/users/info?callback=userinfo_rp_pu
)

[07-Jul-2025 14:08:22 Europe/Moscow] logAdClick DEBUG: Начинаем валидацию initData: user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_i...
[07-Jul-2025 14:08:22 Europe/Moscow] validateTelegramInitData: auth_date старый (но разрешен).
[07-Jul-2025 14:08:22 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[07-Jul-2025 14:08:22 Europe/Moscow] logAdClick DEBUG: Результат validateTelegramInitData: FALSE
[07-Jul-2025 14:08:22 Europe/Moscow] logAdClick WARNING: Стандартная валидация не прошла, пробуем упрощенную
[07-Jul-2025 14:08:22 Europe/Moscow] logAdClick INFO: Упрощенная валидация прошла для пользователя 12345
[07-Jul-2025 14:08:22 Europe/Moscow] logAdClick INFO: initData валидирован для пользователя 12345
[07-Jul-2025 14:08:29 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 14:08:35 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 14:08:35 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 14:08:44 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 14:08:44 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 14:08:44 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 14:08:44 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 14:08:44 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 14:08:44 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 14:08:46 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 14:08:46 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 14:08:46 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 14:08:52 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 14:08:52 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 14:09:17 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 14:09:18 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 14:09:18 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 14:09:18 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 14:09:18 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 14:09:18 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 14:09:18 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 14:09:47 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 14:10:17 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 14:10:47 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 14:11:17 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 14:11:47 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 14:12:17 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 14:12:47 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 14:13:34 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 14:13:39 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 14:13:39 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 14:13:39 Europe/Moscow] DEBUG logAdClick: Получен JSON: {"initData":"user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test","adType":"interstitial","clickType":"button_click","timestamp":1751886819501,"sessionId":"unknown"}
[07-Jul-2025 14:13:39 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 14:13:39 Europe/Moscow] DEBUG logAdClick: Декодированные данные: Array
(
    [initData] => user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test
    [adType] => interstitial
    [clickType] => button_click
    [timestamp] => 1751886819501
    [sessionId] => unknown
)

[07-Jul-2025 14:13:39 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 14:13:39 Europe/Moscow] logAdClick DEBUG: Начинаем валидацию initData: user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_i...
[07-Jul-2025 14:13:39 Europe/Moscow] DEBUG logAdClick: Получен JSON: {"initData":"user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test","adType":"interstitial","clickType":"ad_request","timestamp":1751886819505,"sessionId":"unknown","reason":"User requested ad show"}
[07-Jul-2025 14:13:39 Europe/Moscow] validateTelegramInitData: auth_date старый (но разрешен).
[07-Jul-2025 14:13:39 Europe/Moscow] DEBUG logAdClick: Декодированные данные: Array
(
    [initData] => user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test
    [adType] => interstitial
    [clickType] => ad_request
    [timestamp] => 1751886819505
    [sessionId] => unknown
    [reason] => User requested ad show
)

[07-Jul-2025 14:13:39 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[07-Jul-2025 14:13:39 Europe/Moscow] logAdClick DEBUG: Результат validateTelegramInitData: FALSE
[07-Jul-2025 14:13:39 Europe/Moscow] logAdClick DEBUG: Начинаем валидацию initData: user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_i...
[07-Jul-2025 14:13:39 Europe/Moscow] logAdClick WARNING: Стандартная валидация не прошла, пробуем упрощенную
[07-Jul-2025 14:13:39 Europe/Moscow] validateTelegramInitData: auth_date старый (но разрешен).
[07-Jul-2025 14:13:39 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[07-Jul-2025 14:13:39 Europe/Moscow] logAdClick INFO: Упрощенная валидация прошла для пользователя 12345
[07-Jul-2025 14:13:39 Europe/Moscow] logAdClick DEBUG: Результат validateTelegramInitData: FALSE
[07-Jul-2025 14:13:39 Europe/Moscow] logAdClick INFO: initData валидирован для пользователя 12345
[07-Jul-2025 14:13:39 Europe/Moscow] logAdClick WARNING: Стандартная валидация не прошла, пробуем упрощенную
[07-Jul-2025 14:13:39 Europe/Moscow] logAdClick INFO: Упрощенная валидация прошла для пользователя 12345
[07-Jul-2025 14:13:39 Europe/Moscow] logAdClick INFO: initData валидирован для пользователя 12345
[07-Jul-2025 14:13:46 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 14:13:46 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 14:13:46 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 14:13:46 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 14:13:46 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 14:13:46 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 14:13:46 Europe/Moscow] DEBUG logAdClick: Получен JSON: {"initData":"user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test","adType":"interstitial","clickType":"ad_error","timestamp":1751886826418,"sessionId":"unknown","reason":"Cannot read properties of null (reading 'length')"}
[07-Jul-2025 14:13:46 Europe/Moscow] DEBUG logAdClick: Получен JSON: {"initData":"user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test","adType":"interstitial","clickType":"no_ads_available","timestamp":1751886826419,"sessionId":"unknown","reason":"No ads available from ad network"}
[07-Jul-2025 14:13:46 Europe/Moscow] DEBUG logAdClick: Декодированные данные: Array
(
    [initData] => user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test
    [adType] => interstitial
    [clickType] => ad_error
    [timestamp] => 1751886826418
    [sessionId] => unknown
    [reason] => Cannot read properties of null (reading 'length')
)

[07-Jul-2025 14:13:46 Europe/Moscow] DEBUG logAdClick: Декодированные данные: Array
(
    [initData] => user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test
    [adType] => interstitial
    [clickType] => no_ads_available
    [timestamp] => 1751886826419
    [sessionId] => unknown
    [reason] => No ads available from ad network
)

[07-Jul-2025 14:13:46 Europe/Moscow] logAdClick DEBUG: Начинаем валидацию initData: user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_i...
[07-Jul-2025 14:13:46 Europe/Moscow] logAdClick DEBUG: Начинаем валидацию initData: user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_i...
[07-Jul-2025 14:13:46 Europe/Moscow] validateTelegramInitData: auth_date старый (но разрешен).
[07-Jul-2025 14:13:46 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[07-Jul-2025 14:13:46 Europe/Moscow] validateTelegramInitData: auth_date старый (но разрешен).
[07-Jul-2025 14:13:46 Europe/Moscow] logAdClick DEBUG: Результат validateTelegramInitData: FALSE
[07-Jul-2025 14:13:46 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[07-Jul-2025 14:13:46 Europe/Moscow] logAdClick WARNING: Стандартная валидация не прошла, пробуем упрощенную
[07-Jul-2025 14:13:46 Europe/Moscow] logAdClick DEBUG: Результат validateTelegramInitData: FALSE
[07-Jul-2025 14:13:46 Europe/Moscow] logAdClick INFO: Упрощенная валидация прошла для пользователя 12345
[07-Jul-2025 14:13:46 Europe/Moscow] logAdClick WARNING: Стандартная валидация не прошла, пробуем упрощенную
[07-Jul-2025 14:13:46 Europe/Moscow] logAdClick INFO: initData валидирован для пользователя 12345
[07-Jul-2025 14:13:46 Europe/Moscow] logAdClick INFO: Упрощенная валидация прошла для пользователя 12345
[07-Jul-2025 14:13:46 Europe/Moscow] logAdClick INFO: initData валидирован для пользователя 12345
[07-Jul-2025 14:14:21 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 14:14:21 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 14:14:21 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 14:14:52 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 14:15:22 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 14:15:52 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 14:16:22 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 14:16:52 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 14:17:22 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 14:18:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 14:19:22 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 14:19:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 14:20:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 14:21:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 14:22:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 14:23:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 14:24:22 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 14:24:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 15:45:09 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 15:45:09 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 15:45:09 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 15:45:40 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 15:46:10 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 15:46:40 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 15:47:10 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 15:47:40 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 15:48:10 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 15:48:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 15:49:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 15:50:10 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 15:50:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 15:51:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 15:52:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 15:53:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 15:54:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 15:55:10 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 17:54:42 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 17:54:42 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 17:54:42 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 17:54:52 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 17:54:52 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 17:54:52 Europe/Moscow] DEBUG logAdClick: Получен JSON: {"initData":"user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test","adType":"native_banner","clickType":"ad_request","timestamp":1751900092735,"sessionId":"unknown","reason":"User requested ad show"}
[07-Jul-2025 17:54:52 Europe/Moscow] DEBUG logAdClick: Декодированные данные: Array
(
    [initData] => user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test
    [adType] => native_banner
    [clickType] => ad_request
    [timestamp] => 1751900092735
    [sessionId] => unknown
    [reason] => User requested ad show
)

[07-Jul-2025 17:54:52 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 17:54:52 Europe/Moscow] logAdClick DEBUG: Начинаем валидацию initData: user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_i...
[07-Jul-2025 17:54:52 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 17:54:52 Europe/Moscow] validateTelegramInitData: auth_date старый (но разрешен).
[07-Jul-2025 17:54:52 Europe/Moscow] DEBUG logAdClick: Получен JSON: {"initData":"user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test","adType":"native_banner","clickType":"button_click","timestamp":1751900092732,"sessionId":"unknown"}
[07-Jul-2025 17:54:52 Europe/Moscow] DEBUG logAdClick: Декодированные данные: Array
(
    [initData] => user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test
    [adType] => native_banner
    [clickType] => button_click
    [timestamp] => 1751900092732
    [sessionId] => unknown
)

[07-Jul-2025 17:54:52 Europe/Moscow] logAdClick DEBUG: Начинаем валидацию initData: user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_i...
[07-Jul-2025 17:54:52 Europe/Moscow] validateTelegramInitData: auth_date старый (но разрешен).
[07-Jul-2025 17:54:52 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[07-Jul-2025 17:54:52 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[07-Jul-2025 17:54:52 Europe/Moscow] logAdClick DEBUG: Результат validateTelegramInitData: FALSE
[07-Jul-2025 17:54:52 Europe/Moscow] logAdClick DEBUG: Результат validateTelegramInitData: FALSE
[07-Jul-2025 17:54:52 Europe/Moscow] logAdClick WARNING: Стандартная валидация не прошла, пробуем упрощенную
[07-Jul-2025 17:54:52 Europe/Moscow] logAdClick INFO: Упрощенная валидация прошла для пользователя 12345
[07-Jul-2025 17:54:52 Europe/Moscow] logAdClick WARNING: Стандартная валидация не прошла, пробуем упрощенную
[07-Jul-2025 17:54:52 Europe/Moscow] logAdClick INFO: initData валидирован для пользователя 12345
[07-Jul-2025 17:54:52 Europe/Moscow] logAdClick INFO: Упрощенная валидация прошла для пользователя 12345
[07-Jul-2025 17:54:52 Europe/Moscow] logAdClick INFO: initData валидирован для пользователя 12345
[07-Jul-2025 17:54:53 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 17:54:53 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 17:54:53 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 17:54:53 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 17:54:53 Europe/Moscow] DEBUG logAdClick: Получен JSON: {"initData":"user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test","adType":"native_banner","clickType":"ad_error","timestamp":1751900093684,"sessionId":"unknown","reason":"Cannot read properties of null (reading 'length')"}
[07-Jul-2025 17:54:53 Europe/Moscow] DEBUG logAdClick: Получен JSON: {"initData":"user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test","adType":"native_banner","clickType":"no_ads_available","timestamp":1751900093685,"sessionId":"unknown","reason":"No ads available from ad network"}
[07-Jul-2025 17:54:53 Europe/Moscow] DEBUG logAdClick: Декодированные данные: Array
(
    [initData] => user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test
    [adType] => native_banner
    [clickType] => ad_error
    [timestamp] => 1751900093684
    [sessionId] => unknown
    [reason] => Cannot read properties of null (reading 'length')
)

[07-Jul-2025 17:54:53 Europe/Moscow] DEBUG logAdClick: Декодированные данные: Array
(
    [initData] => user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test
    [adType] => native_banner
    [clickType] => no_ads_available
    [timestamp] => 1751900093685
    [sessionId] => unknown
    [reason] => No ads available from ad network
)

[07-Jul-2025 17:54:53 Europe/Moscow] logAdClick DEBUG: Начинаем валидацию initData: user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_i...
[07-Jul-2025 17:54:53 Europe/Moscow] logAdClick DEBUG: Начинаем валидацию initData: user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_i...
[07-Jul-2025 17:54:53 Europe/Moscow] validateTelegramInitData: auth_date старый (но разрешен).
[07-Jul-2025 17:54:53 Europe/Moscow] validateTelegramInitData: auth_date старый (но разрешен).
[07-Jul-2025 17:54:53 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[07-Jul-2025 17:54:53 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[07-Jul-2025 17:54:53 Europe/Moscow] logAdClick DEBUG: Результат validateTelegramInitData: FALSE
[07-Jul-2025 17:54:53 Europe/Moscow] logAdClick DEBUG: Результат validateTelegramInitData: FALSE
[07-Jul-2025 17:54:53 Europe/Moscow] logAdClick WARNING: Стандартная валидация не прошла, пробуем упрощенную
[07-Jul-2025 17:54:53 Europe/Moscow] logAdClick WARNING: Стандартная валидация не прошла, пробуем упрощенную
[07-Jul-2025 17:54:53 Europe/Moscow] logAdClick INFO: Упрощенная валидация прошла для пользователя 12345
[07-Jul-2025 17:54:53 Europe/Moscow] logAdClick INFO: Упрощенная валидация прошла для пользователя 12345
[07-Jul-2025 17:54:53 Europe/Moscow] logAdClick INFO: initData валидирован для пользователя 12345
[07-Jul-2025 17:54:53 Europe/Moscow] logAdClick INFO: initData валидирован для пользователя 12345
[07-Jul-2025 17:55:12 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 17:55:21 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 17:55:21 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 17:55:22 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 17:55:52 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 17:56:22 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 17:56:52 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 17:57:22 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 17:57:52 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 17:58:22 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 17:59:45 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 17:59:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:00:02 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:00:02 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:00:05 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 18:00:33 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:01:07 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:01:33 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:02:03 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:02:03 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:02:03 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 18:02:10 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:02:10 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:02:10 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 18:02:11 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:02:11 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 18:02:19 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 18:02:19 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 18:02:19 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 18:02:20 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 18:02:20 Europe/Moscow] DEBUG logAdClick: Получен JSON: {"initData":"user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test","adType":"interstitial","clickType":"button_click","timestamp":1751900538989,"sessionId":"unknown"}
[07-Jul-2025 18:02:20 Europe/Moscow] DEBUG logAdClick: Получен JSON: {"initData":"user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test","adType":"interstitial","clickType":"ad_request","timestamp":1751900538995,"sessionId":"unknown","reason":"User requested ad show"}
[07-Jul-2025 18:02:20 Europe/Moscow] DEBUG logAdClick: Декодированные данные: Array
(
    [initData] => user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test
    [adType] => interstitial
    [clickType] => button_click
    [timestamp] => 1751900538989
    [sessionId] => unknown
)

[07-Jul-2025 18:02:20 Europe/Moscow] DEBUG logAdClick: Декодированные данные: Array
(
    [initData] => user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test
    [adType] => interstitial
    [clickType] => ad_request
    [timestamp] => 1751900538995
    [sessionId] => unknown
    [reason] => User requested ad show
)

[07-Jul-2025 18:02:20 Europe/Moscow] logAdClick DEBUG: Начинаем валидацию initData: user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_i...
[07-Jul-2025 18:02:20 Europe/Moscow] logAdClick DEBUG: Начинаем валидацию initData: user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_i...
[07-Jul-2025 18:02:20 Europe/Moscow] validateTelegramInitData: auth_date старый (но разрешен).
[07-Jul-2025 18:02:20 Europe/Moscow] validateTelegramInitData: auth_date старый (но разрешен).
[07-Jul-2025 18:02:20 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[07-Jul-2025 18:02:20 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[07-Jul-2025 18:02:20 Europe/Moscow] logAdClick DEBUG: Результат validateTelegramInitData: FALSE
[07-Jul-2025 18:02:20 Europe/Moscow] logAdClick DEBUG: Результат validateTelegramInitData: FALSE
[07-Jul-2025 18:02:20 Europe/Moscow] logAdClick WARNING: Стандартная валидация не прошла, пробуем упрощенную
[07-Jul-2025 18:02:20 Europe/Moscow] logAdClick WARNING: Стандартная валидация не прошла, пробуем упрощенную
[07-Jul-2025 18:02:20 Europe/Moscow] logAdClick INFO: Упрощенная валидация прошла для пользователя 12345
[07-Jul-2025 18:02:21 Europe/Moscow] logAdClick INFO: Упрощенная валидация прошла для пользователя 12345
[07-Jul-2025 18:02:21 Europe/Moscow] logAdClick INFO: initData валидирован для пользователя 12345
[07-Jul-2025 18:02:21 Europe/Moscow] logAdClick INFO: initData валидирован для пользователя 12345
[07-Jul-2025 18:02:21 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 18:02:21 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 18:02:21 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 18:02:21 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 18:02:21 Europe/Moscow] DEBUG logAdClick: Получен JSON: {"initData":"user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test","adType":"interstitial","clickType":"no_ads_available","timestamp":1751900541922,"sessionId":"unknown","reason":"No ads available from ad network"}
[07-Jul-2025 18:02:21 Europe/Moscow] DEBUG logAdClick: Декодированные данные: Array
(
    [initData] => user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test
    [adType] => interstitial
    [clickType] => no_ads_available
    [timestamp] => 1751900541922
    [sessionId] => unknown
    [reason] => No ads available from ad network
)

[07-Jul-2025 18:02:21 Europe/Moscow] DEBUG logAdClick: Получен JSON: {"initData":"user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test","adType":"interstitial","clickType":"ad_error","timestamp":1751900541921,"sessionId":"unknown","reason":"Cannot read properties of null (reading 'length')"}
[07-Jul-2025 18:02:21 Europe/Moscow] logAdClick DEBUG: Начинаем валидацию initData: user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_i...
[07-Jul-2025 18:02:21 Europe/Moscow] validateTelegramInitData: auth_date старый (но разрешен).
[07-Jul-2025 18:02:21 Europe/Moscow] DEBUG logAdClick: Декодированные данные: Array
(
    [initData] => user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test
    [adType] => interstitial
    [clickType] => ad_error
    [timestamp] => 1751900541921
    [sessionId] => unknown
    [reason] => Cannot read properties of null (reading 'length')
)

[07-Jul-2025 18:02:21 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[07-Jul-2025 18:02:21 Europe/Moscow] logAdClick DEBUG: Результат validateTelegramInitData: FALSE
[07-Jul-2025 18:02:21 Europe/Moscow] logAdClick DEBUG: Начинаем валидацию initData: user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_i...
[07-Jul-2025 18:02:21 Europe/Moscow] logAdClick WARNING: Стандартная валидация не прошла, пробуем упрощенную
[07-Jul-2025 18:02:21 Europe/Moscow] validateTelegramInitData: auth_date старый (но разрешен).
[07-Jul-2025 18:02:22 Europe/Moscow] logAdClick INFO: Упрощенная валидация прошла для пользователя 12345
[07-Jul-2025 18:02:22 Europe/Moscow] logAdClick INFO: initData валидирован для пользователя 12345
[07-Jul-2025 18:02:22 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[07-Jul-2025 18:02:22 Europe/Moscow] logAdClick DEBUG: Результат validateTelegramInitData: FALSE
[07-Jul-2025 18:02:22 Europe/Moscow] logAdClick WARNING: Стандартная валидация не прошла, пробуем упрощенную
[07-Jul-2025 18:02:22 Europe/Moscow] logAdClick INFO: Упрощенная валидация прошла для пользователя 12345
[07-Jul-2025 18:02:22 Europe/Moscow] logAdClick INFO: initData валидирован для пользователя 12345
[07-Jul-2025 18:02:33 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:02:55 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:02:55 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:02:56 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 18:03:25 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:03:56 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:04:26 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:04:56 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:05:26 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:05:56 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:06:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:07:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:07:56 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:08:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:09:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:10:23 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:10:25 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:10:56 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:11:26 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:12:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:12:56 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:13:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:14:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:15:52 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:16:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:17:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:17:56 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:18:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:19:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:20:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:26:28 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:26:28 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:26:28 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 18:26:29 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:26:29 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:26:29 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 18:26:36 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 18:26:36 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 18:26:36 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 18:26:36 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 18:26:36 Europe/Moscow] DEBUG logAdClick: Получен JSON: {"initData":"user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test","adType":"native_banner","clickType":"button_click","timestamp":1751901996354,"sessionId":"unknown"}
[07-Jul-2025 18:26:36 Europe/Moscow] DEBUG logAdClick: Получен JSON: {"initData":"user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test","adType":"native_banner","clickType":"ad_request","timestamp":1751901996356,"sessionId":"unknown","reason":"User requested ad show"}
[07-Jul-2025 18:26:36 Europe/Moscow] DEBUG logAdClick: Декодированные данные: Array
(
    [initData] => user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test
    [adType] => native_banner
    [clickType] => button_click
    [timestamp] => 1751901996354
    [sessionId] => unknown
)

[07-Jul-2025 18:26:36 Europe/Moscow] DEBUG logAdClick: Декодированные данные: Array
(
    [initData] => user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test
    [adType] => native_banner
    [clickType] => ad_request
    [timestamp] => 1751901996356
    [sessionId] => unknown
    [reason] => User requested ad show
)

[07-Jul-2025 18:26:36 Europe/Moscow] logAdClick DEBUG: Начинаем валидацию initData: user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_i...
[07-Jul-2025 18:26:36 Europe/Moscow] logAdClick DEBUG: Начинаем валидацию initData: user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_i...
[07-Jul-2025 18:26:36 Europe/Moscow] validateTelegramInitData: auth_date старый (но разрешен).
[07-Jul-2025 18:26:36 Europe/Moscow] validateTelegramInitData: auth_date старый (но разрешен).
[07-Jul-2025 18:26:36 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[07-Jul-2025 18:26:36 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[07-Jul-2025 18:26:36 Europe/Moscow] logAdClick DEBUG: Результат validateTelegramInitData: FALSE
[07-Jul-2025 18:26:36 Europe/Moscow] logAdClick DEBUG: Результат validateTelegramInitData: FALSE
[07-Jul-2025 18:26:36 Europe/Moscow] logAdClick WARNING: Стандартная валидация не прошла, пробуем упрощенную
[07-Jul-2025 18:26:36 Europe/Moscow] logAdClick WARNING: Стандартная валидация не прошла, пробуем упрощенную
[07-Jul-2025 18:26:36 Europe/Moscow] logAdClick INFO: Упрощенная валидация прошла для пользователя 12345
[07-Jul-2025 18:26:36 Europe/Moscow] logAdClick INFO: Упрощенная валидация прошла для пользователя 12345
[07-Jul-2025 18:26:36 Europe/Moscow] logAdClick INFO: initData валидирован для пользователя 12345
[07-Jul-2025 18:26:36 Europe/Moscow] logAdClick INFO: initData валидирован для пользователя 12345
[07-Jul-2025 18:26:37 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 18:26:37 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 18:26:37 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 18:26:37 Europe/Moscow] DEBUG logAdClick: Получен JSON: {"initData":"user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test","adType":"native_banner","clickType":"no_ads_available","timestamp":1751901997044,"sessionId":"unknown","reason":"No ads available from ad network"}
[07-Jul-2025 18:26:37 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 18:26:37 Europe/Moscow] DEBUG logAdClick: Получен JSON: {"initData":"user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test","adType":"native_banner","clickType":"ad_error","timestamp":1751901997043,"sessionId":"unknown","reason":"Cannot read properties of null (reading 'length')"}
[07-Jul-2025 18:26:37 Europe/Moscow] DEBUG logAdClick: Декодированные данные: Array
(
    [initData] => user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test
    [adType] => native_banner
    [clickType] => no_ads_available
    [timestamp] => 1751901997044
    [sessionId] => unknown
    [reason] => No ads available from ad network
)

[07-Jul-2025 18:26:37 Europe/Moscow] DEBUG logAdClick: Декодированные данные: Array
(
    [initData] => user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test
    [adType] => native_banner
    [clickType] => ad_error
    [timestamp] => 1751901997043
    [sessionId] => unknown
    [reason] => Cannot read properties of null (reading 'length')
)

[07-Jul-2025 18:26:37 Europe/Moscow] logAdClick DEBUG: Начинаем валидацию initData: user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_i...
[07-Jul-2025 18:26:37 Europe/Moscow] logAdClick DEBUG: Начинаем валидацию initData: user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_i...
[07-Jul-2025 18:26:37 Europe/Moscow] validateTelegramInitData: auth_date старый (но разрешен).
[07-Jul-2025 18:26:37 Europe/Moscow] validateTelegramInitData: auth_date старый (но разрешен).
[07-Jul-2025 18:26:37 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[07-Jul-2025 18:26:37 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[07-Jul-2025 18:26:37 Europe/Moscow] logAdClick DEBUG: Результат validateTelegramInitData: FALSE
[07-Jul-2025 18:26:37 Europe/Moscow] logAdClick WARNING: Стандартная валидация не прошла, пробуем упрощенную
[07-Jul-2025 18:26:37 Europe/Moscow] logAdClick DEBUG: Результат validateTelegramInitData: FALSE
[07-Jul-2025 18:26:37 Europe/Moscow] logAdClick INFO: Упрощенная валидация прошла для пользователя 12345
[07-Jul-2025 18:26:37 Europe/Moscow] logAdClick INFO: initData валидирован для пользователя 12345
[07-Jul-2025 18:26:37 Europe/Moscow] logAdClick WARNING: Стандартная валидация не прошла, пробуем упрощенную
[07-Jul-2025 18:26:37 Europe/Moscow] logAdClick INFO: Упрощенная валидация прошла для пользователя 12345
[07-Jul-2025 18:26:37 Europe/Moscow] logAdClick INFO: initData валидирован для пользователя 12345
[07-Jul-2025 18:26:45 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:26:45 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:26:45 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 18:26:52 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 18:26:52 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 18:26:52 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 18:26:52 Europe/Moscow] DEBUG logAdClick: Получен JSON: {"initData":"user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test","adType":"native_banner","clickType":"button_click","timestamp":1751902012087,"sessionId":"unknown"}
[07-Jul-2025 18:26:52 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 18:26:52 Europe/Moscow] DEBUG logAdClick: Декодированные данные: Array
(
    [initData] => user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test
    [adType] => native_banner
    [clickType] => button_click
    [timestamp] => 1751902012087
    [sessionId] => unknown
)

[07-Jul-2025 18:26:52 Europe/Moscow] DEBUG logAdClick: Получен JSON: {"initData":"user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test","adType":"native_banner","clickType":"ad_request","timestamp":1751902012088,"sessionId":"unknown","reason":"User requested ad show"}
[07-Jul-2025 18:26:52 Europe/Moscow] logAdClick DEBUG: Начинаем валидацию initData: user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_i...
[07-Jul-2025 18:26:52 Europe/Moscow] DEBUG logAdClick: Декодированные данные: Array
(
    [initData] => user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test
    [adType] => native_banner
    [clickType] => ad_request
    [timestamp] => 1751902012088
    [sessionId] => unknown
    [reason] => User requested ad show
)

[07-Jul-2025 18:26:52 Europe/Moscow] validateTelegramInitData: auth_date старый (но разрешен).
[07-Jul-2025 18:26:52 Europe/Moscow] logAdClick DEBUG: Начинаем валидацию initData: user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_i...
[07-Jul-2025 18:26:52 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[07-Jul-2025 18:26:52 Europe/Moscow] validateTelegramInitData: auth_date старый (но разрешен).
[07-Jul-2025 18:26:52 Europe/Moscow] logAdClick DEBUG: Результат validateTelegramInitData: FALSE
[07-Jul-2025 18:26:52 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[07-Jul-2025 18:26:52 Europe/Moscow] logAdClick DEBUG: Результат validateTelegramInitData: FALSE
[07-Jul-2025 18:26:52 Europe/Moscow] logAdClick WARNING: Стандартная валидация не прошла, пробуем упрощенную
[07-Jul-2025 18:26:52 Europe/Moscow] logAdClick WARNING: Стандартная валидация не прошла, пробуем упрощенную
[07-Jul-2025 18:26:52 Europe/Moscow] logAdClick INFO: Упрощенная валидация прошла для пользователя 12345
[07-Jul-2025 18:26:52 Europe/Moscow] logAdClick INFO: Упрощенная валидация прошла для пользователя 12345
[07-Jul-2025 18:26:52 Europe/Moscow] logAdClick INFO: initData валидирован для пользователя 12345
[07-Jul-2025 18:26:52 Europe/Moscow] logAdClick INFO: initData валидирован для пользователя 12345
[07-Jul-2025 18:26:52 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 18:26:52 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 18:26:52 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 18:26:52 Europe/Moscow] === НАЧАЛО logAdClick API ===
[07-Jul-2025 18:26:52 Europe/Moscow] DEBUG logAdClick: Получен JSON: {"initData":"user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test","adType":"native_banner","clickType":"no_ads_available","timestamp":1751902012744,"sessionId":"unknown","reason":"No ads available from ad network"}
[07-Jul-2025 18:26:52 Europe/Moscow] DEBUG logAdClick: Получен JSON: {"initData":"user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test","adType":"native_banner","clickType":"ad_error","timestamp":1751902012744,"sessionId":"unknown","reason":"Cannot read properties of null (reading 'length')"}
[07-Jul-2025 18:26:52 Europe/Moscow] DEBUG logAdClick: Декодированные данные: Array
(
    [initData] => user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test
    [adType] => native_banner
    [clickType] => no_ads_available
    [timestamp] => 1751902012744
    [sessionId] => unknown
    [reason] => No ads available from ad network
)

[07-Jul-2025 18:26:52 Europe/Moscow] DEBUG logAdClick: Декодированные данные: Array
(
    [initData] => user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test
    [adType] => native_banner
    [clickType] => ad_error
    [timestamp] => 1751902012744
    [sessionId] => unknown
    [reason] => Cannot read properties of null (reading 'length')
)

[07-Jul-2025 18:26:52 Europe/Moscow] logAdClick DEBUG: Начинаем валидацию initData: user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_i...
[07-Jul-2025 18:26:52 Europe/Moscow] logAdClick DEBUG: Начинаем валидацию initData: user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_i...
[07-Jul-2025 18:26:52 Europe/Moscow] validateTelegramInitData: auth_date старый (но разрешен).
[07-Jul-2025 18:26:52 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[07-Jul-2025 18:26:52 Europe/Moscow] validateTelegramInitData: auth_date старый (но разрешен).
[07-Jul-2025 18:26:52 Europe/Moscow] logAdClick DEBUG: Результат validateTelegramInitData: FALSE
[07-Jul-2025 18:26:52 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[07-Jul-2025 18:26:52 Europe/Moscow] logAdClick WARNING: Стандартная валидация не прошла, пробуем упрощенную
[07-Jul-2025 18:26:52 Europe/Moscow] logAdClick DEBUG: Результат validateTelegramInitData: FALSE
[07-Jul-2025 18:26:52 Europe/Moscow] logAdClick INFO: Упрощенная валидация прошла для пользователя 12345
[07-Jul-2025 18:26:52 Europe/Moscow] logAdClick WARNING: Стандартная валидация не прошла, пробуем упрощенную
[07-Jul-2025 18:26:52 Europe/Moscow] logAdClick INFO: initData валидирован для пользователя 12345
[07-Jul-2025 18:26:52 Europe/Moscow] logAdClick INFO: Упрощенная валидация прошла для пользователя 12345
[07-Jul-2025 18:26:52 Europe/Moscow] logAdClick INFO: initData валидирован для пользователя 12345
[07-Jul-2025 18:27:39 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:27:39 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:27:44 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 18:28:09 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:28:40 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:29:10 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:29:40 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:30:10 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:30:41 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:31:10 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:31:40 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:32:40 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:32:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:33:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:34:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:35:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:36:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:37:40 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:37:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:38:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:39:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:51:29 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:51:29 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:51:29 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\functions.php:16) in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php on line 9
[07-Jul-2025 18:52:00 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:52:30 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:53:00 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:53:30 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:54:00 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:54:30 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:55:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:56:30 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:56:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:57:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:58:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 18:59:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 19:00:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 19:01:30 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 19:01:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 19:02:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 19:03:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 19:04:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 19:05:51 Europe/Moscow] PHP Fatal error:  Cannot redeclare loadUserData() (previously declared in D:\OSPanel\domains\argun-defolt.loc\api\db_mock.php:10) in D:\OSPanel\domains\argun-defolt.loc\api\functions.php on line 15
[07-Jul-2025 19:56:31 Europe/Moscow] getWithdrawalHistory DEBUG: Получен запрос: {"initData":"fallback_test"}
[07-Jul-2025 19:56:31 Europe/Moscow] getWithdrawalHistory INFO: Получен initData (длина: 13): fallback_test...
[07-Jul-2025 19:56:31 Europe/Moscow] validateTelegramInitData: Отсутствует hash, auth_date или user.
[07-Jul-2025 19:56:31 Europe/Moscow] getWithdrawalHistory ERROR: Валидация initData не прошла
[07-Jul-2025 19:56:35 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 19:56:35 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 19:56:38 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 19:56:38 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 19:56:41 Europe/Moscow] fraud-detection INFO: Получен запрос на анализ фрода
[07-Jul-2025 19:56:41 Europe/Moscow] fraud-detection INFO: Получен запрос на анализ фрода
[07-Jul-2025 19:56:47 Europe/Moscow] fraud-detection INFO: Получен запрос на анализ фрода
[07-Jul-2025 19:56:50 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 19:56:50 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 19:56:52 Europe/Moscow] fraud-detection INFO: Получен запрос на анализ фрода
[07-Jul-2025 19:56:52 Europe/Moscow] fraud-detection INFO: Получен запрос на анализ фрода
[07-Jul-2025 20:09:41 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 20:09:41 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 20:09:43 Europe/Moscow] fraud-detection INFO: Получен запрос на анализ фрода
[07-Jul-2025 20:09:43 Europe/Moscow] fraud-detection INFO: Получен запрос на анализ фрода
[07-Jul-2025 20:09:55 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 20:09:55 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 20:09:57 Europe/Moscow] fraud-detection INFO: Получен запрос на анализ фрода
[07-Jul-2025 20:09:57 Europe/Moscow] fraud-detection INFO: Получен запрос на анализ фрода
[07-Jul-2025 20:11:48 Europe/Moscow] getWithdrawalHistory DEBUG: Получен запрос: {"initData":"fallback_test"}
[07-Jul-2025 20:11:48 Europe/Moscow] getWithdrawalHistory INFO: Получен initData (длина: 13): fallback_test...
[07-Jul-2025 20:11:48 Europe/Moscow] validateTelegramInitData: Отсутствует hash, auth_date или user.
[07-Jul-2025 20:11:48 Europe/Moscow] getWithdrawalHistory ERROR: Валидация initData не прошла
[07-Jul-2025 20:11:51 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc/api/user_data.json
[07-Jul-2025 20:11:51 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[07-Jul-2025 20:11:53 Europe/Moscow] fraud-detection INFO: Получен запрос на анализ фрода
[07-Jul-2025 20:11:53 Europe/Moscow] fraud-detection INFO: Получен запрос на анализ фрода
[07-Jul-2025 20:11:55 Europe/Moscow] fraud-detection INFO: Получен запрос на анализ фрода
