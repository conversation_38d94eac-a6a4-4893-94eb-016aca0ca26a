<?php
/**
 * api/admin/security.php
 * Страница безопасности
 */

// Включаем логирование
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../error.log');
error_reporting(E_ALL);

// Подключаем файл с функциями аутентификации и авторизации
require_once __DIR__ . '/auth.php';

// Проверяем аутентификацию
session_start();
if (!isAuthenticated()) {
    // Если пользователь не аутентифицирован, перенаправляем на страницу входа
    header('Location: login.php');
    exit;
}

// --- Подключение зависимостей с проверкой ---
if (!(@require_once __DIR__ . '/../config.php')) { 
    http_response_code(500); 
    error_log('FATAL: config.php not found in admin/security.php'); 
    die('Ошибка: Не удалось загрузить config.php'); 
}
if (!(@require_once __DIR__ . '/../db_mock.php')) { 
    http_response_code(500); 
    error_log('FATAL: db_mock.php not found in admin/security.php'); 
    die('Ошибка: Не удалось загрузить db_mock.php'); 
}
if (!(@require_once __DIR__ . '/../security.php')) { 
    http_response_code(500); 
    error_log('FATAL: security.php not found in admin/security.php'); 
    die('Ошибка: Не удалось загрузить security.php'); 
}
// --- Конец проверки зависимостей ---

// Загрузка данных пользователей
$userData = loadUserData();
if (!is_array($userData)) {
    die('Ошибка: Не удалось загрузить данные пользователей');
}

// Обработка действий
$actionMessage = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && isset($_POST['user_id'])) {
    $userId = intval($_POST['user_id']);
    
    switch ($_POST['action']) {
        case 'unblock_user':
            if (isset($userData[$userId])) {
                $userData[$userId]['blocked'] = false;
                $userData[$userId]['suspicious_activity'] = 0;
                
                if (saveUserData($userData)) {
                    $actionMessage = "Пользователь $userId успешно разблокирован";
                    
                    // Логируем разблокировку пользователя
                    logAuditEvent('admin_unblock_user', $userId, [
                        'admin_username' => $_SESSION['admin_username']
                    ]);
                } else {
                    $actionMessage = "Ошибка: Не удалось сохранить данные пользователя $userId";
                }
            } else {
                $actionMessage = "Ошибка: Пользователь $userId не найден";
            }
            break;
            
        case 'block_user':
            if (isset($userData[$userId])) {
                $userData[$userId]['blocked'] = true;
                $userData[$userId]['blocked_at'] = time();
                
                if (saveUserData($userData)) {
                    $actionMessage = "Пользователь $userId успешно заблокирован";
                    
                    // Логируем блокировку пользователя
                    logAuditEvent('admin_block_user', $userId, [
                        'admin_username' => $_SESSION['admin_username']
                    ]);
                } else {
                    $actionMessage = "Ошибка: Не удалось сохранить данные пользователя $userId";
                }
            } else {
                $actionMessage = "Ошибка: Пользователь $userId не найден";
            }
            break;
            
        case 'reset_suspicious':
            if (isset($userData[$userId])) {
                $userData[$userId]['suspicious_activity'] = 0;
                
                if (saveUserData($userData)) {
                    $actionMessage = "Счетчик подозрительной активности пользователя $userId успешно сброшен";
                    
                    // Логируем сброс счетчика подозрительной активности
                    logAuditEvent('admin_reset_suspicious', $userId, [
                        'admin_username' => $_SESSION['admin_username']
                    ]);
                } else {
                    $actionMessage = "Ошибка: Не удалось сохранить данные пользователя $userId";
                }
            } else {
                $actionMessage = "Ошибка: Пользователь $userId не найден";
            }
            break;
    }
}

// Получение журнала аудита
$auditLog = [];
$auditLogFile = __DIR__ . '/../audit.log';
if (file_exists($auditLogFile)) {
    $auditLog = array_slice(array_reverse(file($auditLogFile)), 0, 100);
}

// Фильтрация пользователей с подозрительной активностью
$suspiciousUsers = [];
foreach ($userData as $userId => $user) {
    if (isset($user['suspicious_activity']) && $user['suspicious_activity'] > 0) {
        $suspiciousUsers[$userId] = $user;
    }
}

// Сортировка пользователей с подозрительной активностью по убыванию счетчика
uasort($suspiciousUsers, function($a, $b) {
    return ($b['suspicious_activity'] ?? 0) - ($a['suspicious_activity'] ?? 0);
});

// Фильтрация заблокированных пользователей
$blockedUsers = [];
foreach ($userData as $userId => $user) {
    if (isset($user['blocked']) && $user['blocked']) {
        $blockedUsers[$userId] = $user;
    }
}

// Сортировка заблокированных пользователей по дате блокировки
uasort($blockedUsers, function($a, $b) {
    return ($b['blocked_at'] ?? 0) - ($a['blocked_at'] ?? 0);
});

// Обработка POST запроса для переключения лимитов
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['toggle_limits'])) {
    $settingsFile = __DIR__ . '/ad_limits_settings.json';

    try {
        // Загружаем текущие настройки
        if (!file_exists($settingsFile)) {
            $settings = [
                'disable_all_ad_limits' => false,
                'last_updated' => null,
                'updated_by' => null
            ];
        } else {
            $content = file_get_contents($settingsFile);
            $settings = json_decode($content, true);
            if (!$settings) {
                $settings = [
                    'disable_all_ad_limits' => false,
                    'last_updated' => null,
                    'updated_by' => null
                ];
            }
        }

        // Переключаем состояние
        $oldValue = $settings['disable_all_ad_limits'];
        $newValue = !$oldValue;

        $settings['disable_all_ad_limits'] = $newValue;
        $settings['last_updated'] = date('Y-m-d H:i:s');
        $settings['updated_by'] = $_SESSION['admin_username'] ?? 'admin';

        // Сохраняем настройки
        $result = file_put_contents($settingsFile, json_encode($settings, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

        if ($result !== false) {
            $action = $newValue ? 'ОТКЛЮЧЕНЫ' : 'ВКЛЮЧЕНЫ';
            error_log("AD_LIMITS_CONTROL: Лимиты рекламы {$action} администратором через POST");

            $successMessage = $newValue ? 'Все лимиты рекламы отключены!' : 'Лимиты рекламы включены!';
        } else {
            $errorMessage = 'Ошибка сохранения настроек';
        }

    } catch (Exception $e) {
        error_log("security.php: Ошибка переключения лимитов: " . $e->getMessage());
        $errorMessage = 'Ошибка: ' . $e->getMessage();
    }
}

// Получаем текущие настройки лимитов
$limitsSettings = null;
$settingsFile = __DIR__ . '/ad_limits_settings.json';
try {
    if (file_exists($settingsFile)) {
        $content = file_get_contents($settingsFile);
        $limitsSettings = json_decode($content, true);
    }
    if (!$limitsSettings) {
        $limitsSettings = ['disable_all_ad_limits' => false];
    }
} catch (Exception $e) {
    error_log("security.php: Ошибка загрузки настроек лимитов: " . $e->getMessage());
    $limitsSettings = ['disable_all_ad_limits' => false];
}

// Получаем данные о лимитах для встраивания в страницу
try {
    $limitsData = getCurrentLimits();
    $limitsJson = json_encode($limitsData, JSON_UNESCAPED_UNICODE);
} catch (Exception $e) {
    error_log("security.php: Ошибка получения лимитов: " . $e->getMessage());
    $limitsData = null;
    $limitsJson = 'null';
}

// Подключаем шаблон заголовка
include 'templates/header.php';
?>

<style>
.card.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.card.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
.log-entry {
    padding: 0.25rem 0;
    border-bottom: 1px solid #e3e6f0;
}
.log-entry:last-child {
    border-bottom: none;
}
#peak-hours-list .badge {
    margin: 0.1rem;
}
.table th {
    background-color: #f8f9fc;
    border-color: #e3e6f0;
}
.spinner-border {
    width: 2rem;
    height: 2rem;
}
</style>

<div class="container-fluid">
    <div class="row">        <?php include 'templates/sidebar.php'; ?>

        <!-- Основное содержимое -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Безопасность</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="security.php" class="btn btn-sm btn-outline-secondary">Обновить</a>
                    </div>
                </div>
            </div>

            <?php if (!empty($actionMessage)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo $actionMessage; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <!-- Вкладки -->
            <ul class="nav nav-tabs mb-4" id="securityTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="suspicious-tab" data-bs-toggle="tab" data-bs-target="#suspicious" type="button" role="tab" aria-controls="suspicious" aria-selected="true">
                        Подозрительная активность <span class="badge bg-warning"><?php echo count($suspiciousUsers); ?></span>
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="blocked-tab" data-bs-toggle="tab" data-bs-target="#blocked" type="button" role="tab" aria-controls="blocked" aria-selected="false">
                        Заблокированные пользователи <span class="badge bg-danger"><?php echo count($blockedUsers); ?></span>
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="audit-tab" data-bs-toggle="tab" data-bs-target="#audit" type="button" role="tab" aria-controls="audit" aria-selected="false">
                        Журнал аудита
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="limits-tab" data-bs-toggle="tab" data-bs-target="#limits" type="button" role="tab" aria-controls="limits" aria-selected="false">
                        Лимиты системы <span class="badge bg-info" id="peak-time-badge"></span>
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="antifraud-tab" data-bs-toggle="tab" data-bs-target="#antifraud" type="button" role="tab" aria-controls="antifraud" aria-selected="false">
                        🛡️ Антифрод система <span class="badge bg-success" id="antifraud-status-badge">Активна</span>
                    </button>
                </li>
            </ul>

            <!-- Содержимое вкладок -->
            <div class="tab-content" id="securityTabsContent">
                <!-- Подозрительная активность -->
                <div class="tab-pane fade show active" id="suspicious" role="tabpanel" aria-labelledby="suspicious-tab">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Пользователи с подозрительной активностью</h6>
                        </div>
                        <div class="card-body">
                            <?php if (empty($suspiciousUsers)): ?>
                                <p class="text-center">Нет пользователей с подозрительной активностью</p>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th>ID пользователя</th>
                                                <th>Уровень подозрительности</th>
                                                <th>Баланс</th>
                                                <th>Дата регистрации</th>
                                                <th>Действия</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($suspiciousUsers as $userId => $user): ?>
                                                <tr>
                                                    <td><?php echo $userId; ?></td>
                                                    <td>
                                                        <div class="progress">
                                                            <div class="progress-bar bg-warning" role="progressbar" style="width: <?php echo min(100, ($user['suspicious_activity'] / SUSPICIOUS_ACTIVITY_THRESHOLD) * 100); ?>%" aria-valuenow="<?php echo $user['suspicious_activity']; ?>" aria-valuemin="0" aria-valuemax="<?php echo SUSPICIOUS_ACTIVITY_THRESHOLD; ?>">
                                                                <?php echo $user['suspicious_activity']; ?>/<?php echo SUSPICIOUS_ACTIVITY_THRESHOLD; ?>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td><?php echo $user['balance'] ?? 0; ?></td>
                                                    <td><?php echo isset($user['joined']) ? date('Y-m-d H:i', $user['joined']) : 'Неизвестно'; ?></td>
                                                    <td>
                                                        <form method="post" class="d-inline">
                                                            <input type="hidden" name="action" value="block_user">
                                                            <input type="hidden" name="user_id" value="<?php echo $userId; ?>">
                                                            <button type="submit" class="btn btn-sm btn-danger">
                                                                <i class="bi bi-lock"></i> Заблокировать
                                                            </button>
                                                        </form>
                                                        <form method="post" class="d-inline">
                                                            <input type="hidden" name="action" value="reset_suspicious">
                                                            <input type="hidden" name="user_id" value="<?php echo $userId; ?>">
                                                            <button type="submit" class="btn btn-sm btn-warning">
                                                                <i class="bi bi-arrow-counterclockwise"></i> Сбросить
                                                            </button>
                                                        </form>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Заблокированные пользователи -->
                <div class="tab-pane fade" id="blocked" role="tabpanel" aria-labelledby="blocked-tab">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Заблокированные пользователи</h6>
                        </div>
                        <div class="card-body">
                            <?php if (empty($blockedUsers)): ?>
                                <p class="text-center">Нет заблокированных пользователей</p>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th>ID пользователя</th>
                                                <th>Дата блокировки</th>
                                                <th>Баланс</th>
                                                <th>Подозрительная активность</th>
                                                <th>Действия</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($blockedUsers as $userId => $user): ?>
                                                <tr>
                                                    <td><?php echo $userId; ?></td>
                                                    <td><?php echo isset($user['blocked_at']) ? date('Y-m-d H:i', $user['blocked_at']) : 'Неизвестно'; ?></td>
                                                    <td><?php echo $user['balance'] ?? 0; ?></td>
                                                    <td><?php echo $user['suspicious_activity'] ?? 0; ?></td>
                                                    <td>
                                                        <form method="post">
                                                            <input type="hidden" name="action" value="unblock_user">
                                                            <input type="hidden" name="user_id" value="<?php echo $userId; ?>">
                                                            <button type="submit" class="btn btn-sm btn-success">
                                                                <i class="bi bi-unlock"></i> Разблокировать
                                                            </button>
                                                        </form>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Журнал аудита -->
                <div class="tab-pane fade" id="audit" role="tabpanel" aria-labelledby="audit-tab">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                            <h6 class="m-0 font-weight-bold text-primary">Журнал аудита (последние 100 записей)</h6>
                            <form method="post">
                                <input type="hidden" name="action" value="clear_audit_log">
                                <button type="submit" class="btn btn-sm btn-outline-danger">Очистить журнал</button>
                            </form>
                        </div>
                        <div class="card-body">
                            <?php if (empty($auditLog)): ?>
                                <p class="text-center">Журнал аудита пуст</p>
                            <?php else: ?>
                                <div style="max-height: 500px; overflow-y: auto;">
                                    <?php foreach ($auditLog as $logEntry): ?>
                                        <div class="log-entry small"><?php echo htmlspecialchars($logEntry); ?></div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Лимиты системы -->
                <div class="tab-pane fade" id="limits" role="tabpanel" aria-labelledby="limits-tab">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3 d-flex justify-content-between align-items-center">
                            <h6 class="m-0 font-weight-bold text-primary">Текущие лимиты системы безопасности</h6>
                            <div>
                                <button class="btn btn-sm btn-outline-secondary me-2" onclick="testSession()">
                                    <i class="fas fa-bug"></i> Тест сессии
                                </button>
                                <button class="btn btn-sm btn-outline-primary" onclick="refreshLimits()">
                                    <i class="fas fa-sync-alt"></i> Обновить
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- Сообщения об успехе/ошибке -->
                            <?php if (isset($successMessage)): ?>
                                <div class="alert alert-success alert-dismissible fade show" role="alert">
                                    <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($successMessage); ?>
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            <?php endif; ?>

                            <?php if (isset($errorMessage)): ?>
                                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                    <i class="fas fa-exclamation-circle"></i> <?php echo htmlspecialchars($errorMessage); ?>
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            <?php endif; ?>

                            <!-- Управление лимитами -->
                            <div class="alert alert-warning mb-4" id="limits-control-section">
                                <div class="row align-items-center">
                                    <div class="col-md-8">
                                        <h5 class="alert-heading mb-2">
                                            <i class="fas fa-exclamation-triangle"></i> Управление лимитами рекламы
                                        </h5>
                                        <p class="mb-0">
                                            <strong>Внимание:</strong> Отключение лимитов полностью снимает ограничения на просмотры рекламы.
                                            IP-ограничения и блокировка подозрительных пользователей остаются активными.
                                        </p>
                                    </div>
                                    <div class="col-md-4 text-end">
                                        <form method="POST" style="display: inline;" id="limitsToggleForm">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="disableAllLimits"
                                                       <?php echo $limitsSettings['disable_all_ad_limits'] ? 'checked' : ''; ?>
                                                       onchange="if(confirmToggleLimits(this)) this.form.submit();">
                                                <input type="hidden" name="toggle_limits" value="1">
                                                <label class="form-check-label fw-bold" for="disableAllLimits">
                                                    Отключить все лимиты рекламы
                                                </label>
                                            </div>
                                        </form>
                                        <small class="<?php echo $limitsSettings['disable_all_ad_limits'] ? 'text-danger fw-bold' : 'text-success'; ?>">
                                            <?php echo $limitsSettings['disable_all_ad_limits'] ? 'Все лимиты рекламы отключены' : 'Лимиты рекламы активны'; ?>
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <div id="limits-loading" class="text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Загрузка...</span>
                                </div>
                                <p class="mt-2">Загрузка информации о лимитах...</p>
                            </div>

                            <div id="limits-content" style="display: none;">
                                <!-- Статус системы -->
                                <div class="row mb-4">
                                    <div class="col-md-6">
                                        <div class="card border-left-primary">
                                            <div class="card-body">
                                                <div class="row no-gutters align-items-center">
                                                    <div class="col mr-2">
                                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                                            Статус системы
                                                        </div>
                                                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="system-status">
                                                            Загрузка...
                                                        </div>
                                                    </div>
                                                    <div class="col-auto">
                                                        <i class="fas fa-clock fa-2x text-gray-300"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card border-left-info">
                                            <div class="card-body">
                                                <div class="row no-gutters align-items-center">
                                                    <div class="col mr-2">
                                                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                                            Время UTC
                                                        </div>
                                                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="current-time">
                                                            Загрузка...
                                                        </div>
                                                    </div>
                                                    <div class="col-auto">
                                                        <i class="fas fa-globe fa-2x text-gray-300"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Лимиты рекламы по типам -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6 class="m-0 font-weight-bold text-primary">Лимиты просмотров рекламы по типам</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-bordered">
                                                <thead>
                                                    <tr>
                                                        <th>Параметр</th>
                                                        <th>Базовый лимит</th>
                                                        <th>Текущий лимит</th>
                                                        <th>Множитель</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td><strong>Просмотров в час (по типу)</strong></td>
                                                        <td><span id="base-type-hour">-</span></td>
                                                        <td><span id="current-type-hour" class="font-weight-bold">-</span></td>
                                                        <td><span id="multiplier-type-hour" class="badge">-</span></td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong>Просмотров в день (по типу)</strong></td>
                                                        <td><span id="base-type-day">-</span></td>
                                                        <td><span id="current-type-day" class="font-weight-bold">-</span></td>
                                                        <td><span id="multiplier-type-day" class="badge">-</span></td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>

                                <!-- Общие лимиты -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6 class="m-0 font-weight-bold text-primary">Общие лимиты системы</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-bordered">
                                                <thead>
                                                    <tr>
                                                        <th>Параметр</th>
                                                        <th>Базовый лимит</th>
                                                        <th>Текущий лимит</th>
                                                        <th>Множитель</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td><strong>Общих просмотров в час</strong></td>
                                                        <td><span id="base-general-hour">-</span></td>
                                                        <td><span id="current-general-hour" class="font-weight-bold">-</span></td>
                                                        <td><span id="multiplier-general-hour" class="badge">-</span></td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong>Общих просмотров в день</strong></td>
                                                        <td><span id="base-general-day">-</span></td>
                                                        <td><span id="current-general-day" class="font-weight-bold">-</span></td>
                                                        <td><span id="multiplier-general-day" class="badge">-</span></td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong>Запросов в минуту (IP)</strong></td>
                                                        <td><span id="base-requests-minute">-</span></td>
                                                        <td><span id="current-requests-minute" class="font-weight-bold">-</span></td>
                                                        <td><span class="badge bg-secondary">1x</span></td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong>Порог подозрительной активности</strong></td>
                                                        <td><span id="base-suspicious">-</span></td>
                                                        <td><span id="current-suspicious" class="font-weight-bold">-</span></td>
                                                        <td><span class="badge bg-secondary">1x</span></td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>

                                <!-- Пиковые часы -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6 class="m-0 font-weight-bold text-primary">Настройки пиковых часов</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <p><strong>Пиковые часы (UTC):</strong></p>
                                                <div id="peak-hours-list" class="mb-3">
                                                    Загрузка...
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <p><strong>Множители в пиковое время:</strong></p>
                                                <ul>
                                                    <li>Часовые лимиты: <span class="badge bg-warning">x2.0</span></li>
                                                    <li>Дневные лимиты: <span class="badge bg-info">x1.5</span></li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Антифрод система -->
                <div class="tab-pane fade" id="antifraud" role="tabpanel" aria-labelledby="antifraud-tab">
                    <div class="row">
                        <!-- Статистика антифрод -->
                        <div class="col-md-6">
                            <div class="card shadow mb-4">
                                <div class="card-header py-3">
                                    <h6 class="m-0 font-weight-bold text-primary">📊 Статистика антифрод системы</h6>
                                </div>
                                <div class="card-body">
                                    <div id="antifraud-stats-loading" class="text-center">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Загрузка...</span>
                                        </div>
                                        <p class="mt-2">Загрузка статистики...</p>
                                    </div>
                                    <div id="antifraud-stats-content" style="display: none;">
                                        <div class="row">
                                            <div class="col-6">
                                                <div class="text-center">
                                                    <h4 class="text-success" id="total-fingerprints">0</h4>
                                                    <small class="text-muted">Всего отпечатков</small>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="text-center">
                                                    <h4 class="text-danger" id="blocked-devices">0</h4>
                                                    <small class="text-muted">Заблокировано устройств</small>
                                                </div>
                                            </div>
                                        </div>
                                        <hr>
                                        <div class="row">
                                            <div class="col-6">
                                                <div class="text-center">
                                                    <h4 class="text-warning" id="duplicate-fingerprints">0</h4>
                                                    <small class="text-muted">Дубликаты отпечатков</small>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="text-center">
                                                    <h4 class="text-info" id="fraud-attempts">0</h4>
                                                    <small class="text-muted">Попытки фрода</small>
                                                </div>
                                            </div>
                                        </div>
                                        <hr>
                                        <div class="row">
                                            <div class="col-6">
                                                <div class="text-center">
                                                    <h4 class="text-info" id="vpn-detections">0</h4>
                                                    <small class="text-muted">🌐 VPN детекций</small>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="text-center">
                                                    <h4 class="text-danger" id="vpn-blocked">0</h4>
                                                    <small class="text-muted">🚫 VPN заблокировано</small>
                                                </div>
                                            </div>
                                        </div>
                                        <hr>
                                        <div class="row">
                                            <div class="col-6">
                                                <div class="text-center">
                                                    <h4 class="text-primary" id="desktop-devices-count">0</h4>
                                                    <small class="text-muted">🖥️ Десктоп устройств</small>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="text-center">
                                                    <h4 class="text-info" id="mobile-devices-count">0</h4>
                                                    <small class="text-muted">📱 Мобильных устройств</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Настройки антифрод -->
                        <div class="col-md-6">
                            <div class="card shadow mb-4">
                                <div class="card-header py-3">
                                    <h6 class="m-0 font-weight-bold text-primary">⚙️ Настройки антифрод системы</h6>
                                </div>
                                <div class="card-body">
                                    <form id="antifraud-settings-form">
                                        <div class="mb-3">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="enable-antifraud" checked>
                                                <label class="form-check-label" for="enable-antifraud">
                                                    Включить антифрод систему
                                                </label>
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="fraud-threshold" class="form-label">Порог фрода (баллы)</label>
                                            <input type="number" class="form-control" id="fraud-threshold" value="50" min="10" max="100">
                                            <div class="form-text">При превышении этого порога пользователь будет заблокирован</div>
                                        </div>

                                        <div class="mb-3">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="block-vpn" checked>
                                                <label class="form-check-label" for="block-vpn">
                                                    Блокировать VPN/Proxy
                                                </label>
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="block-duplicate-fingerprints" checked>
                                                <label class="form-check-label" for="block-duplicate-fingerprints">
                                                    Блокировать дубликаты отпечатков
                                                </label>
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="block-self-referrals" checked>
                                                <label class="form-check-label" for="block-self-referrals">
                                                    Блокировать самореферралы
                                                </label>
                                            </div>
                                        </div>

                                        <hr class="my-4">

                                        <div class="mb-3">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="admin-immunity">
                                                <label class="form-check-label" for="admin-immunity">
                                                    <strong>👑 Иммунитет администратора</strong>
                                                </label>
                                                <div class="form-text">Если включено, администратор (ID: 5880288830) не подвергается никаким блокировкам</div>
                                            </div>
                                        </div>

                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save"></i> Сохранить настройки
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Журнал фрода -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3 d-flex justify-content-between align-items-center">
                            <h6 class="m-0 font-weight-bold text-primary">🚨 Журнал фрода (последние 50 записей)</h6>
                            <div>
                                <button class="btn btn-sm btn-outline-primary" onclick="refreshFraudLog()">
                                    <i class="fas fa-sync-alt"></i> Обновить
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="clearFraudLog()">
                                    <i class="fas fa-trash"></i> Очистить
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="fraud-log-loading" class="text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Загрузка...</span>
                                </div>
                                <p class="mt-2">Загрузка журнала фрода...</p>
                            </div>
                            <div id="fraud-log-content" style="display: none;">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>Время</th>
                                                <th>Пользователь</th>
                                                <th>Тип нарушения</th>
                                                <th>Риск</th>
                                                <th>Баллы</th>
                                                <th>Действие</th>
                                                <th>Детали</th>
                                            </tr>
                                        </thead>
                                        <tbody id="fraud-log-table">
                                            <!-- Данные загружаются через JavaScript -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Управление заблокированными устройствами -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">🔒 Заблокированные устройства</h6>
                        </div>
                        <div class="card-body">
                            <div id="blocked-devices-loading" class="text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Загрузка...</span>
                                </div>
                                <p class="mt-2">Загрузка заблокированных устройств...</p>
                            </div>
                            <div id="blocked-devices-content" style="display: none;">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>Отпечаток устройства</th>
                                                <th>Пользователи</th>
                                                <th>Дата блокировки</th>
                                                <th>Причина</th>
                                                <th>Действия</th>
                                            </tr>
                                        </thead>
                                        <tbody id="blocked-devices-table">
                                            <!-- Данные загружаются через JavaScript -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Отпечатки устройств -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3 d-flex justify-content-between align-items-center">
                            <h6 class="m-0 font-weight-bold text-primary">📱 Отпечатки устройств</h6>
                            <div class="d-flex gap-2">
                                <button class="btn btn-sm btn-outline-success" onclick="exportFingerprints()">
                                    <i class="fas fa-download"></i> Экспорт CSV
                                </button>
                                <button class="btn btn-sm btn-outline-primary" onclick="refreshFingerprints()">
                                    <i class="fas fa-sync-alt"></i> Обновить
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- Фильтры -->
                            <div class="row mb-3">
                                <div class="col-md-3">
                                    <input type="text" class="form-control form-control-sm" id="fingerprint-search" placeholder="Поиск по отпечатку, ID, IP...">
                                </div>
                                <div class="col-md-2">
                                    <select class="form-control form-control-sm" id="device-type-filter">
                                        <option value="">Все устройства</option>
                                        <option value="desktop">Десктоп</option>
                                        <option value="mobile">Мобильные</option>
                                        <option value="tablet">Планшеты</option>
                                        <option value="unknown">Неизвестно</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <select class="form-control form-control-sm" id="risk-level-filter">
                                        <option value="">Все уровни риска</option>
                                        <option value="minimal">Минимальный</option>
                                        <option value="low">Низкий</option>
                                        <option value="medium">Средний</option>
                                        <option value="high">Высокий</option>
                                        <option value="critical">Критический</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <select class="form-control form-control-sm" id="sort-by-filter">
                                        <option value="created_at">По дате</option>
                                        <option value="risk_score">По риску</option>
                                        <option value="user_id">По ID</option>
                                        <option value="device_type">По типу</option>
                                    </select>
                                </div>
                                <div class="col-md-1">
                                    <select class="form-control form-control-sm" id="sort-order-filter">
                                        <option value="desc">↓</option>
                                        <option value="asc">↑</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <button class="btn btn-sm btn-primary w-100" onclick="applyFingerprintFilters()">
                                        <i class="fas fa-filter"></i> Применить
                                    </button>
                                </div>
                            </div>

                            <!-- Статистика -->
                            <div class="row mb-3">
                                <div class="col-md-12">
                                    <div class="alert alert-info">
                                        <div class="row text-center">
                                            <div class="col-md-2">
                                                <strong id="total-fingerprints-stat">0</strong><br>
                                                <small>Всего отпечатков</small>
                                            </div>
                                            <div class="col-md-2">
                                                <strong id="desktop-devices-stat">0</strong><br>
                                                <small>Десктоп</small>
                                            </div>
                                            <div class="col-md-2">
                                                <strong id="mobile-devices-stat">0</strong><br>
                                                <small>Мобильные</small>
                                            </div>
                                            <div class="col-md-2">
                                                <strong id="tablet-devices-stat">0</strong><br>
                                                <small>Планшеты</small>
                                            </div>
                                            <div class="col-md-2">
                                                <strong id="high-risk-stat">0</strong><br>
                                                <small>Высокий риск</small>
                                            </div>
                                            <div class="col-md-2">
                                                <strong id="critical-risk-stat">0</strong><br>
                                                <small>Критический риск</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Загрузка -->
                            <div id="fingerprints-loading" class="text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Загрузка...</span>
                                </div>
                                <p class="mt-2">Загрузка отпечатков устройств...</p>
                            </div>

                            <!-- Таблица отпечатков -->
                            <div id="fingerprints-content" style="display: none;">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-hover table-sm">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>Отпечаток</th>
                                                <th>ID пользователя</th>
                                                <th>IP адрес</th>
                                                <th>Тип устройства</th>
                                                <th>Платформа</th>
                                                <th>Разрешение</th>
                                                <th>Риск</th>
                                                <th>Дата</th>
                                                <th>Действия</th>
                                            </tr>
                                        </thead>
                                        <tbody id="fingerprints-table">
                                            <!-- Данные загружаются через JavaScript -->
                                        </tbody>
                                    </table>
                                </div>

                                <!-- Пагинация -->
                                <div class="d-flex justify-content-between align-items-center mt-3">
                                    <div>
                                        <span id="fingerprints-info">Показано 0 из 0 записей</span>
                                    </div>
                                    <nav>
                                        <ul class="pagination pagination-sm mb-0" id="fingerprints-pagination">
                                            <!-- Пагинация генерируется через JavaScript -->
                                        </ul>
                                    </nav>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
// Встроенные данные о лимитах
const embeddedLimitsData = <?php echo $limitsJson; ?>;

// Встроенные настройки лимитов
const embeddedLimitsSettings = <?php echo json_encode($limitsSettings, JSON_UNESCAPED_UNICODE); ?>;

// Функция для загрузки настроек лимитов (теперь использует встроенные данные)
function loadLimitsSettings() {
    console.log('Используем встроенные настройки лимитов:', embeddedLimitsSettings);

    const checkbox = document.getElementById('disableAllLimits');
    const statusText = document.getElementById('limits-status-text');

    if (checkbox && embeddedLimitsSettings) {
        checkbox.checked = embeddedLimitsSettings.disable_all_ad_limits;
    }

    if (statusText && embeddedLimitsSettings) {
        if (embeddedLimitsSettings.disable_all_ad_limits) {
            statusText.textContent = 'Все лимиты рекламы отключены';
            statusText.className = 'text-danger fw-bold';
        } else {
            statusText.textContent = 'Лимиты рекламы активны';
            statusText.className = 'text-success';
        }
    }
}

// Функция для загрузки информации о лимитах
function loadLimitsInfo() {
    document.getElementById('limits-loading').style.display = 'block';
    document.getElementById('limits-content').style.display = 'none';

    // Сначала пробуем использовать встроенные данные
    if (embeddedLimitsData) {
        console.log('Используем встроенные данные о лимитах');
        const mockResponse = {
            success: true,
            timestamp: Math.floor(Date.now() / 1000),
            current_time_utc: new Date().toISOString().slice(0, 19).replace('T', ' '),
            current_time_local: new Date().toLocaleString(),
            limits_info: embeddedLimitsData,
            status: embeddedLimitsData.is_peak_time ? 'PEAK_TIME' : 'NORMAL_TIME',
            multipliers: {
                peak_hourly_multiplier: 2.0,
                peak_daily_multiplier: 1.5
            }
        };

        updateLimitsDisplay(mockResponse);
        document.getElementById('limits-loading').style.display = 'none';
        document.getElementById('limits-content').style.display = 'block';
        return;
    }

    // Если встроенных данных нет, делаем AJAX запрос
    fetch('current_limits.php', {
        method: 'GET',
        credentials: 'same-origin', // Включаем cookies для сессии
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
        .then(response => {
            if (!response.ok) {
                if (response.status === 401) {
                    throw new Error('Сессия истекла. Пожалуйста, войдите в систему заново.');
                }
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                updateLimitsDisplay(data);
                document.getElementById('limits-loading').style.display = 'none';
                document.getElementById('limits-content').style.display = 'block';
            } else {
                throw new Error(data.error || 'Ошибка загрузки данных');
            }
        })
        .catch(error => {
            console.error('Ошибка загрузки лимитов:', error);
            document.getElementById('limits-loading').innerHTML =
                '<div class="alert alert-danger"><strong>Ошибка загрузки данных о лимитах:</strong><br>' + error.message +
                '<br><button class="btn btn-sm btn-primary mt-2" onclick="loadLimitsInfo()">Попробовать снова</button>' +
                '<br><button class="btn btn-sm btn-secondary mt-2" onclick="testSession()">Тест сессии</button></div>';
        });
}

// Функция для обновления отображения лимитов
function updateLimitsDisplay(data) {
    const limits = data.limits_info.limits;
    const baseLimits = data.limits_info.base_limits;
    const isPeakTime = data.limits_info.is_peak_time;

    // Используем встроенные настройки для определения состояния лимитов
    const limitsDisabled = embeddedLimitsSettings ? embeddedLimitsSettings.disable_all_ad_limits : false;

    // Обновляем переключатель лимитов (но не меняем его состояние, так как это управляется формой)
    const statusText = document.getElementById('limits-status-text');

    if (statusText) {
        if (limitsDisabled) {
            statusText.textContent = 'Все лимиты рекламы отключены';
            statusText.className = 'text-danger fw-bold';
        } else {
            statusText.textContent = 'Лимиты рекламы активны';
            statusText.className = 'text-success';
        }
    }

    // Обновляем статус системы
    if (limitsDisabled) {
        document.getElementById('system-status').textContent = 'ЛИМИТЫ ОТКЛЮЧЕНЫ';
        document.getElementById('system-status').className = 'h5 mb-0 font-weight-bold text-danger';
    } else {
        document.getElementById('system-status').textContent =
            isPeakTime ? 'ПИКОВОЕ ВРЕМЯ' : 'ОБЫЧНОЕ ВРЕМЯ';
        document.getElementById('system-status').className =
            'h5 mb-0 font-weight-bold ' + (isPeakTime ? 'text-warning' : 'text-success');
    }

    // Обновляем время
    document.getElementById('current-time').textContent = data.current_time_utc;

    // Обновляем badge в заголовке вкладки
    const badge = document.getElementById('peak-time-badge');
    if (limitsDisabled) {
        badge.textContent = 'ОТКЛЮЧЕНЫ';
        badge.className = 'badge bg-danger';
    } else if (isPeakTime) {
        badge.textContent = 'ПИКОВОЕ';
        badge.className = 'badge bg-warning';
    } else {
        badge.textContent = 'ОБЫЧНОЕ';
        badge.className = 'badge bg-success';
    }

    // Лимиты по типам рекламы
    document.getElementById('base-type-hour').textContent = baseLimits.ad_views_per_type_per_hour;
    document.getElementById('current-type-hour').textContent = limitsDisabled ? '∞' : limits.ad_views_per_type_per_hour;
    updateMultiplierBadge('multiplier-type-hour', baseLimits.ad_views_per_type_per_hour, limits.ad_views_per_type_per_hour, limitsDisabled);

    document.getElementById('base-type-day').textContent = baseLimits.ad_views_per_type_per_day;
    document.getElementById('current-type-day').textContent = limitsDisabled ? '∞' : limits.ad_views_per_type_per_day;
    updateMultiplierBadge('multiplier-type-day', baseLimits.ad_views_per_type_per_day, limits.ad_views_per_type_per_day, limitsDisabled);

    // Общие лимиты
    document.getElementById('base-general-hour').textContent = baseLimits.ad_views_per_hour;
    document.getElementById('current-general-hour').textContent = limitsDisabled ? '∞' : limits.ad_views_per_hour;
    updateMultiplierBadge('multiplier-general-hour', baseLimits.ad_views_per_hour, limits.ad_views_per_hour, limitsDisabled);

    document.getElementById('base-general-day').textContent = baseLimits.ad_views_per_day;
    document.getElementById('current-general-day').textContent = limitsDisabled ? '∞' : limits.ad_views_per_day;
    updateMultiplierBadge('multiplier-general-day', baseLimits.ad_views_per_day, limits.ad_views_per_day, limitsDisabled);

    // Статические лимиты
    document.getElementById('base-requests-minute').textContent = limits.requests_per_minute;
    document.getElementById('current-requests-minute').textContent = limits.requests_per_minute;

    document.getElementById('base-suspicious').textContent = limits.suspicious_activity_threshold;
    document.getElementById('current-suspicious').textContent = limits.suspicious_activity_threshold;

    // Пиковые часы
    const peakHours = data.limits_info.peak_hours.map(h => h + ':00').join(', ');
    document.getElementById('peak-hours-list').innerHTML =
        '<span class="badge bg-info me-1">' + peakHours.split(', ').join('</span> <span class="badge bg-info me-1">') + '</span>';
}

// Функция для обновления badge множителя
function updateMultiplierBadge(elementId, baseValue, currentValue, limitsDisabled = false) {
    const element = document.getElementById(elementId);

    if (limitsDisabled) {
        element.textContent = '∞';
        element.className = 'badge bg-danger';
        return;
    }

    const multiplier = currentValue / baseValue;

    if (multiplier === 1) {
        element.textContent = '1x';
        element.className = 'badge bg-secondary';
    } else if (multiplier === 1.5) {
        element.textContent = '1.5x';
        element.className = 'badge bg-info';
    } else if (multiplier === 2) {
        element.textContent = '2x';
        element.className = 'badge bg-warning';
    } else {
        element.textContent = multiplier.toFixed(1) + 'x';
        element.className = 'badge bg-primary';
    }
}

// Функция для обновления лимитов
function refreshLimits() {
    loadLimitsInfo();
}

// Функция подтверждения для переключения лимитов
function confirmToggleLimits(checkbox) {
    const newState = checkbox.checked;

    if (newState) {
        if (!confirm('Вы уверены, что хотите ОТКЛЮЧИТЬ все лимиты рекламы?\n\nЭто позволит пользователям смотреть неограниченное количество рекламы.')) {
            checkbox.checked = false;
            return false;
        }
    }

    return true;
}

// Функция для тестирования сессии
function testSession() {
    fetch('test_session.php', {
        method: 'GET',
        credentials: 'same-origin',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        console.log('Данные сессии:', data);
        alert('Данные сессии выведены в консоль браузера (F12)');
    })
    .catch(error => {
        console.error('Ошибка тестирования сессии:', error);
        alert('Ошибка тестирования сессии: ' + error.message);
    });
}

// Загружаем данные при переключении на вкладку лимитов
document.addEventListener('DOMContentLoaded', function() {
    const limitsTab = document.getElementById('limits-tab');
    if (limitsTab) {
        limitsTab.addEventListener('shown.bs.tab', function() {
            loadLimitsSettings(); // Загружаем настройки переключателя (из встроенных данных)
            loadLimitsInfo();     // Затем загружаем информацию о лимитах
        });
    }

    // Автообновление каждые 30 секунд, если вкладка активна
    setInterval(function() {
        const limitsPane = document.getElementById('limits');
        if (limitsPane && limitsPane.classList.contains('active')) {
            loadLimitsInfo();     // Обновляем только лимиты (настройки не меняются без перезагрузки)
        }
    }, 30000);

    // Обработчик для антифрод вкладки
    const antifraudTab = document.getElementById('antifraud-tab');
    if (antifraudTab) {
        antifraudTab.addEventListener('shown.bs.tab', function() {
            loadAntifraudData();
            loadAntifraudSettings();

            // Небольшая задержка для загрузки DOM элементов
            setTimeout(function() {
                loadDeviceFingerprints(); // Загружаем отпечатки устройств
            }, 100);
        });
    }

    // Обработчик формы настроек антифрод
    const antifraudForm = document.getElementById('antifraud-settings-form');
    if (antifraudForm) {
        antifraudForm.addEventListener('submit', function(e) {
            e.preventDefault();
            saveAntifraudSettings();
        });
    }

    // Обработчики для фильтров отпечатков
    const searchInput = document.getElementById('fingerprint-search');
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                applyFingerprintFilters();
            }
        });
    }
});

// === ФУНКЦИИ ДЛЯ АНТИФРОД СИСТЕМЫ ===

// Загрузка настроек антифрод системы
function loadAntifraudSettings() {
    console.log('Загрузка настроек антифрод системы...');

    fetch('../fraud-detection.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            action: 'get_admin_settings'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.settings) {
            updateAntifraudSettingsForm(data.settings);
        } else {
            console.error('Ошибка загрузки настроек антифрод:', data.error);
        }
    })
    .catch(error => {
        console.error('Ошибка запроса настроек антифрод:', error);
    });
}

// Обновление формы настроек
function updateAntifraudSettingsForm(settings) {
    document.getElementById('enable-antifraud').checked = settings.enable_antifraud || false;
    document.getElementById('fraud-threshold').value = settings.fraud_threshold || 50;
    document.getElementById('block-vpn').checked = settings.block_vpn || false;
    document.getElementById('block-duplicate-fingerprints').checked = settings.block_duplicate_fingerprints || false;
    document.getElementById('block-self-referrals').checked = settings.block_self_referrals || false;
    document.getElementById('admin-immunity').checked = settings.admin_immunity || false;

    // Обновляем статусный бейдж
    const statusBadge = document.getElementById('antifraud-status-badge');
    if (statusBadge) {
        if (settings.enable_antifraud) {
            statusBadge.textContent = 'Активна';
            statusBadge.className = 'badge bg-success';
        } else {
            statusBadge.textContent = 'Отключена';
            statusBadge.className = 'badge bg-danger';
        }
    }
}

// Загрузка данных антифрод системы
function loadAntifraudData() {
    console.log('Загрузка данных антифрод системы...');

    // Показываем индикаторы загрузки
    document.getElementById('antifraud-stats-loading').style.display = 'block';
    document.getElementById('antifraud-stats-content').style.display = 'none';
    document.getElementById('fraud-log-loading').style.display = 'block';
    document.getElementById('fraud-log-content').style.display = 'none';
    document.getElementById('blocked-devices-loading').style.display = 'block';
    document.getElementById('blocked-devices-content').style.display = 'none';

    // Загружаем статистику
    fetch('../fraud-detection.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            action: 'get_admin_stats'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateAntifraudStats(data.stats);
            updateFraudLog(data.fraud_log);
            updateBlockedDevices(data.blocked_devices);
        } else {
            console.error('Ошибка загрузки данных антифрод:', data.error);
        }
    })
    .catch(error => {
        console.error('Ошибка запроса антифрод данных:', error);
    });
}

// Обновление статистики антифрод
function updateAntifraudStats(stats) {
    document.getElementById('total-fingerprints').textContent = stats.total_fingerprints || 0;
    document.getElementById('blocked-devices').textContent = stats.blocked_devices || 0;
    document.getElementById('duplicate-fingerprints').textContent = stats.duplicate_fingerprints || 0;
    document.getElementById('fraud-attempts').textContent = stats.fraud_attempts || 0;
    document.getElementById('vpn-detections').textContent = stats.vpn_detections || 0;
    document.getElementById('vpn-blocked').textContent = stats.vpn_blocked || 0;

    // Загружаем дополнительную статистику по типам устройств
    loadDeviceTypesStats();

    // Скрываем загрузку и показываем контент
    document.getElementById('antifraud-stats-loading').style.display = 'none';
    document.getElementById('antifraud-stats-content').style.display = 'block';
}

// Загрузка статистики по типам устройств
function loadDeviceTypesStats() {
    fetch('../fraud-detection.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            action: 'get_device_fingerprints',
            page: 1,
            limit: 1 // Нам нужна только статистика
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.stats) {
            const deviceTypes = data.stats.device_types || {};
            document.getElementById('desktop-devices-count').textContent = deviceTypes.desktop || 0;
            document.getElementById('mobile-devices-count').textContent = (deviceTypes.mobile || 0) + (deviceTypes.tablet || 0);
        }
    })
    .catch(error => {
        console.error('Ошибка загрузки статистики устройств:', error);
    });
}

// Обновление журнала фрода
function updateFraudLog(fraudLog) {
    const tableBody = document.getElementById('fraud-log-table');
    tableBody.innerHTML = '';

    if (!fraudLog || fraudLog.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="7" class="text-center">Нет записей о фроде</td></tr>';
    } else {
        fraudLog.forEach(entry => {
            const row = document.createElement('tr');

            // Определяем класс для уровня риска
            let riskClass = 'badge bg-secondary';
            if (entry.risk_level === 'CRITICAL') riskClass = 'badge bg-danger';
            else if (entry.risk_level === 'HIGH') riskClass = 'badge bg-warning';
            else if (entry.risk_level === 'MEDIUM') riskClass = 'badge bg-info';
            else if (entry.risk_level === 'LOW') riskClass = 'badge bg-success';

            row.innerHTML = `
                <td>${new Date(entry.timestamp).toLocaleString('ru-RU')}</td>
                <td>${entry.user_id || 'Неизвестен'}</td>
                <td>${entry.violation_type || 'Неизвестно'}</td>
                <td><span class="${riskClass}">${entry.risk_level || 'UNKNOWN'}</span></td>
                <td>${entry.risk_score || 0}</td>
                <td>${entry.action || 'Нет действия'}</td>
                <td><small>${entry.details || 'Нет деталей'}</small></td>
            `;

            tableBody.appendChild(row);
        });
    }

    // Скрываем загрузку и показываем контент
    document.getElementById('fraud-log-loading').style.display = 'none';
    document.getElementById('fraud-log-content').style.display = 'block';
}

// Обновление списка заблокированных устройств
function updateBlockedDevices(blockedDevices) {
    const tableBody = document.getElementById('blocked-devices-table');
    tableBody.innerHTML = '';

    if (!blockedDevices || blockedDevices.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="5" class="text-center">Нет заблокированных устройств</td></tr>';
    } else {
        blockedDevices.forEach(device => {
            const row = document.createElement('tr');

            row.innerHTML = `
                <td><code>${device.fingerprint.substring(0, 16)}...</code></td>
                <td>${device.user_count || 0} пользователей</td>
                <td>${new Date(device.blocked_at).toLocaleString('ru-RU')}</td>
                <td>${device.reason || 'Неизвестно'}</td>
                <td>
                    <button class="btn btn-sm btn-outline-success" onclick="unblockDevice('${device.fingerprint}')">
                        <i class="fas fa-unlock"></i> Разблокировать
                    </button>
                </td>
            `;

            tableBody.appendChild(row);
        });
    }

    // Скрываем загрузку и показываем контент
    document.getElementById('blocked-devices-loading').style.display = 'none';
    document.getElementById('blocked-devices-content').style.display = 'block';
}

// Сохранение настроек антифрод
function saveAntifraudSettings() {
    const settings = {
        enable_antifraud: document.getElementById('enable-antifraud').checked,
        fraud_threshold: parseInt(document.getElementById('fraud-threshold').value),
        block_vpn: document.getElementById('block-vpn').checked,
        block_duplicate_fingerprints: document.getElementById('block-duplicate-fingerprints').checked,
        block_self_referrals: document.getElementById('block-self-referrals').checked,
        admin_immunity: document.getElementById('admin-immunity').checked
    };

    fetch('../fraud-detection.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            action: 'save_admin_settings',
            settings: settings
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Настройки антифрод системы сохранены!');
        } else {
            alert('Ошибка сохранения настроек: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Ошибка сохранения настроек:', error);
        alert('Ошибка сохранения настроек');
    });
}

// Обновление журнала фрода
function refreshFraudLog() {
    loadAntifraudData();
}

// Очистка журнала фрода
function clearFraudLog() {
    if (confirm('Вы уверены, что хотите очистить журнал фрода?')) {
        fetch('../fraud-detection.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                action: 'clear_fraud_log'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Журнал фрода очищен!');
                loadAntifraudData();
            } else {
                alert('Ошибка очистки журнала: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Ошибка очистки журнала:', error);
            alert('Ошибка очистки журнала');
        });
    }
}

// Разблокировка устройства
function unblockDevice(fingerprint) {
    if (confirm('Вы уверены, что хотите разблокировать это устройство?')) {
        fetch('../fraud-detection.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                action: 'unblock_device',
                fingerprint: fingerprint
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Устройство разблокировано!');
                loadAntifraudData();
            } else {
                alert('Ошибка разблокировки устройства: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Ошибка разблокировки устройства:', error);
            alert('Ошибка разблокировки устройства');
        });
    }
}

// === ФУНКЦИИ ДЛЯ ОТПЕЧАТКОВ УСТРОЙСТВ ===

// Глобальные переменные для отпечатков
let currentFingerprintsPage = 1;
let fingerprintsFilters = {
    search: '',
    device_type: '',
    risk_level: '',
    sort_by: 'created_at',
    sort_order: 'desc'
};

// Загрузка отпечатков устройств
function loadDeviceFingerprints(page = 1) {
    currentFingerprintsPage = page;

    const loadingElement = document.getElementById('fingerprints-loading');
    const contentElement = document.getElementById('fingerprints-content');

    if (!loadingElement || !contentElement) {
        console.error('Элементы отпечатков не найдены в DOM');
        return;
    }

    loadingElement.style.display = 'block';
    contentElement.style.display = 'none';

    const requestData = {
        action: 'get_device_fingerprints',
        page: page,
        limit: 20,
        ...fingerprintsFilters
    };

    fetch('../fraud-detection.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
    })
    .then(response => response.json())
    .then(data => {
        document.getElementById('fingerprints-loading').style.display = 'none';

        if (data.success) {
            displayFingerprints(data.data);
            updateFingerprintsPagination(data.pagination);
            updateFingerprintsStats(data.stats);
            document.getElementById('fingerprints-content').style.display = 'block';
        } else {
            console.error('Ошибка загрузки отпечатков:', data.error);
            alert('Ошибка загрузки отпечатков устройств: ' + data.error);
        }
    })
    .catch(error => {
        document.getElementById('fingerprints-loading').style.display = 'none';
        console.error('Ошибка запроса отпечатков:', error);
        alert('Ошибка загрузки отпечатков устройств');
    });
}

// Отображение отпечатков в таблице
function displayFingerprints(fingerprints) {
    const tableBody = document.getElementById('fingerprints-table');
    tableBody.innerHTML = '';

    if (!fingerprints || fingerprints.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="9" class="text-center">Отпечатки не найдены</td></tr>';
        return;
    }

    fingerprints.forEach(fingerprint => {
        const row = document.createElement('tr');

        // Определяем цвет риска
        const riskColors = {
            'minimal': 'success',
            'low': 'info',
            'medium': 'warning',
            'high': 'danger',
            'critical': 'dark'
        };

        const riskColor = riskColors[fingerprint.risk_level] || 'secondary';

        // Определяем иконку типа устройства
        const deviceIcons = {
            'desktop': '🖥️',
            'mobile': '📱',
            'tablet': '📱',
            'unknown': '❓'
        };

        const deviceIcon = deviceIcons[fingerprint.device_type] || '❓';

        row.innerHTML = `
            <td>
                <code title="${fingerprint.fingerprint}">${String(fingerprint.fingerprint).substring(0, 12)}...</code>
            </td>
            <td>
                <strong>${fingerprint.user_id}</strong>
            </td>
            <td>
                <span class="text-muted">${fingerprint.ip_address}</span>
            </td>
            <td>
                ${deviceIcon} ${fingerprint.device_type}
            </td>
            <td>
                <small>${fingerprint.platform}</small>
            </td>
            <td>
                <small>${fingerprint.screen_resolution}</small>
            </td>
            <td>
                <span class="badge bg-${riskColor}">${fingerprint.risk_score}</span>
                <br><small class="text-muted">${fingerprint.risk_level}</small>
            </td>
            <td>
                <small>${fingerprint.timestamp}</small>
            </td>
            <td>
                <button class="btn btn-sm btn-outline-info" onclick="viewFingerprintDetails('${String(fingerprint.fingerprint)}')" title="Подробности">
                    <i class="fas fa-eye"></i>
                </button>
            </td>
        `;

        tableBody.appendChild(row);
    });
}

// Обновление пагинации
function updateFingerprintsPagination(pagination) {
    const paginationContainer = document.getElementById('fingerprints-pagination');
    const infoContainer = document.getElementById('fingerprints-info');

    // Обновляем информацию
    const start = (pagination.page - 1) * pagination.limit + 1;
    const end = Math.min(pagination.page * pagination.limit, pagination.total);
    infoContainer.textContent = `Показано ${start}-${end} из ${pagination.total} записей`;

    // Очищаем пагинацию
    paginationContainer.innerHTML = '';

    if (pagination.total_pages <= 1) return;

    // Кнопка "Предыдущая"
    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${!pagination.has_prev ? 'disabled' : ''}`;
    prevLi.innerHTML = `<a class="page-link" href="#" onclick="loadDeviceFingerprints(${pagination.page - 1})">‹</a>`;
    paginationContainer.appendChild(prevLi);

    // Номера страниц
    const startPage = Math.max(1, pagination.page - 2);
    const endPage = Math.min(pagination.total_pages, pagination.page + 2);

    for (let i = startPage; i <= endPage; i++) {
        const li = document.createElement('li');
        li.className = `page-item ${i === pagination.page ? 'active' : ''}`;
        li.innerHTML = `<a class="page-link" href="#" onclick="loadDeviceFingerprints(${i})">${i}</a>`;
        paginationContainer.appendChild(li);
    }

    // Кнопка "Следующая"
    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${!pagination.has_next ? 'disabled' : ''}`;
    nextLi.innerHTML = `<a class="page-link" href="#" onclick="loadDeviceFingerprints(${pagination.page + 1})">›</a>`;
    paginationContainer.appendChild(nextLi);
}

// Обновление статистики
function updateFingerprintsStats(stats) {
    document.getElementById('total-fingerprints-stat').textContent = stats.total_fingerprints || 0;
    document.getElementById('desktop-devices-stat').textContent = stats.device_types.desktop || 0;
    document.getElementById('mobile-devices-stat').textContent = stats.device_types.mobile || 0;
    document.getElementById('tablet-devices-stat').textContent = stats.device_types.tablet || 0;
    document.getElementById('high-risk-stat').textContent = (stats.risk_distribution.high || 0) + (stats.risk_distribution.critical || 0);
    document.getElementById('critical-risk-stat').textContent = stats.risk_distribution.critical || 0;
}

// Применение фильтров
function applyFingerprintFilters() {
    fingerprintsFilters.search = document.getElementById('fingerprint-search').value;
    fingerprintsFilters.device_type = document.getElementById('device-type-filter').value;
    fingerprintsFilters.risk_level = document.getElementById('risk-level-filter').value;
    fingerprintsFilters.sort_by = document.getElementById('sort-by-filter').value;
    fingerprintsFilters.sort_order = document.getElementById('sort-order-filter').value;

    loadDeviceFingerprints(1);
}

// Обновление отпечатков
function refreshFingerprints() {
    loadDeviceFingerprints(currentFingerprintsPage);
}

// Экспорт отпечатков в CSV
function exportFingerprints() {
    const requestData = {
        action: 'export_device_fingerprints',
        format: 'csv',
        filters: fingerprintsFilters
    };

    fetch('../fraud-detection.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Создаем и скачиваем файл
            const blob = new Blob([data.content], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', data.filename);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            alert(`Экспорт завершен! Скачано ${data.total_records} записей.`);
        } else {
            alert('Ошибка экспорта: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Ошибка экспорта:', error);
        alert('Ошибка экспорта данных');
    });
}

// Просмотр деталей отпечатка
function viewFingerprintDetails(fingerprint) {
    // Создаем модальное окно для отображения деталей
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Детали отпечатка устройства</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Загрузка...</span>
                        </div>
                        <p class="mt-2">Загрузка деталей...</p>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    // Загружаем детали отпечатка
    fetch('../fraud-detection.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            action: 'get_device_risk_profile',
            fingerprint: fingerprint
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayFingerprintDetails(modal, data.profile);
        } else {
            modal.querySelector('.modal-body').innerHTML = `
                <div class="alert alert-danger">
                    Ошибка загрузки деталей: ${data.error}
                </div>
            `;
        }
    })
    .catch(error => {
        console.error('Ошибка загрузки деталей:', error);
        modal.querySelector('.modal-body').innerHTML = `
            <div class="alert alert-danger">
                Ошибка загрузки деталей отпечатка
            </div>
        `;
    });

    // Удаляем модальное окно после закрытия
    modal.addEventListener('hidden.bs.modal', function() {
        document.body.removeChild(modal);
    });
}

// Отображение деталей отпечатка в модальном окне
function displayFingerprintDetails(modal, profile) {
    const deviceInfo = profile.device_info || {};
    const riskFactors = profile.risk_factors || [];

    modal.querySelector('.modal-body').innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <h6>Основная информация</h6>
                <table class="table table-sm">
                    <tr><td><strong>Отпечаток:</strong></td><td><code>${String(profile.fingerprint || '')}</code></td></tr>
                    <tr><td><strong>ID пользователя:</strong></td><td>${profile.user_id}</td></tr>
                    <tr><td><strong>IP адрес:</strong></td><td>${profile.ip_address}</td></tr>
                    <tr><td><strong>Первое обнаружение:</strong></td><td>${profile.first_seen}</td></tr>
                    <tr><td><strong>Последнее обнаружение:</strong></td><td>${profile.last_seen}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>Информация об устройстве</h6>
                <table class="table table-sm">
                    <tr><td><strong>Тип устройства:</strong></td><td>${deviceInfo.type || 'unknown'}</td></tr>
                    <tr><td><strong>Платформа:</strong></td><td>${deviceInfo.platform || 'unknown'}</td></tr>
                    <tr><td><strong>Размер экрана:</strong></td><td>${deviceInfo.screen_size || 'unknown'}</td></tr>
                    <tr><td><strong>Touch points:</strong></td><td>${deviceInfo.touch_points || 0}</td></tr>
                    <tr><td><strong>Репутация:</strong></td><td><span class="badge bg-${getReputationColor(profile.reputation)}">${profile.reputation}</span></td></tr>
                </table>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col-md-12">
                <h6>Оценка рисков</h6>
                <div class="progress mb-2">
                    <div class="progress-bar bg-${getRiskColor(profile.risk_score)}" style="width: ${Math.min(profile.risk_score, 100)}%">
                        ${profile.risk_score} баллов
                    </div>
                </div>

                ${riskFactors.length > 0 ? `
                    <h6 class="mt-3">Факторы риска</h6>
                    <ul class="list-group list-group-flush">
                        ${riskFactors.map(factor => `
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                ${factor.type}
                                <span class="badge bg-warning">${factor.score || 0} баллов</span>
                            </li>
                        `).join('')}
                    </ul>
                ` : '<p class="text-muted">Факторы риска не обнаружены</p>'}

                ${profile.fraud_history && profile.fraud_history.length > 0 ? `
                    <h6 class="mt-3">История нарушений</h6>
                    <div class="alert alert-warning">
                        Обнаружено ${profile.fraud_history.length} нарушений
                    </div>
                ` : ''}
            </div>
        </div>
    `;
}

// Вспомогательные функции для цветов
function getReputationColor(reputation) {
    const colors = {
        'clean': 'success',
        'questionable': 'warning',
        'suspicious': 'danger',
        'malicious': 'dark'
    };
    return colors[reputation] || 'secondary';
}

function getRiskColor(riskScore) {
    if (riskScore >= 80) return 'danger';
    if (riskScore >= 60) return 'warning';
    if (riskScore >= 40) return 'info';
    return 'success';
}

</script>

<?php
// Подключаем шаблон подвала
include 'templates/footer.php';
?>
