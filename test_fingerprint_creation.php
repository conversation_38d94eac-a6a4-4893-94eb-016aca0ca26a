<?php
/**
 * Тест создания отпечатков устройств
 */

echo "<h1>Тест создания отпечатков устройств</h1>";

// Тестовые данные
$testData = [
    'action' => 'register_device',
    'initData' => 'test_fallback_mode_12345',
    'fingerprint_data' => [
        'fingerprint' => 'test_fingerprint_' . time(),
        'components' => [
            'device_type' => 'desktop',
            'platform' => 'Windows',
            'screen_resolution' => '1920x1080',
            'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'timezone' => 'Europe/Moscow',
            'language' => 'ru-RU',
            'canvas_fingerprint' => 'test_canvas_' . time(),
            'webgl_fingerprint' => 'test_webgl_' . time()
        ]
    ],
    'vpn_data' => [
        'is_vpn_detected' => false,
        'risk_score' => 10,
        'indicators' => []
    ]
];

echo "<h2>Отправляем тестовые данные...</h2>";
echo "<pre>" . json_encode($testData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";

// Отправляем запрос
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://argun-defolt.loc/api/fraud-detection.php');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($testData));
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "<h2>Ответ сервера (HTTP $httpCode):</h2>";
echo "<pre>" . $response . "</pre>";

// Проверяем файлы
echo "<h2>Проверяем созданные файлы:</h2>";

$userDataFile = 'database/user_data.json';
if (file_exists($userDataFile)) {
    $userData = json_decode(file_get_contents($userDataFile), true);
    if (isset($userData['12345'])) {
        echo "<p>✅ Пользователь 12345 создан в user_data.json</p>";
        echo "<pre>" . json_encode($userData['12345'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
    } else {
        echo "<p>❌ Пользователь 12345 НЕ найден в user_data.json</p>";
    }
} else {
    echo "<p>❌ Файл user_data.json не найден</p>";
}

$fingerprintsFile = 'database/advanced_fingerprints.json';
if (file_exists($fingerprintsFile)) {
    $fingerprints = json_decode(file_get_contents($fingerprintsFile), true);
    if (!empty($fingerprints)) {
        echo "<p>✅ Отпечатки найдены в advanced_fingerprints.json (" . count($fingerprints) . " шт.)</p>";
        echo "<pre>" . json_encode(array_slice($fingerprints, -1, 1, true), JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
    } else {
        echo "<p>❌ Отпечатки НЕ найдены в advanced_fingerprints.json</p>";
    }
} else {
    echo "<p>❌ Файл advanced_fingerprints.json не найден</p>";
}

echo "<h2>Логи ошибок:</h2>";
$errorLog = 'database/error.log';
if (file_exists($errorLog)) {
    $logs = file_get_contents($errorLog);
    $recentLogs = array_slice(explode("\n", $logs), -10);
    echo "<pre>" . implode("\n", $recentLogs) . "</pre>";
} else {
    echo "<p>Файл error.log не найден</p>";
}
?>
