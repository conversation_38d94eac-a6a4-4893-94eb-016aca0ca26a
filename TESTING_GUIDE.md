# 🧪 Руководство по тестированию системы логирования рекламы

## Обзор изменений

Система логирования рекламы была значительно улучшена для отслеживания всех взаимодействий пользователей с рекламными кнопками.

### Новые возможности:

1. **Полное отслеживание кликов** - все клики по кнопкам логируются независимо от результата
2. **Расширенное определение стран** - поддержка основных стран мира
3. **Детальная статистика ошибок** - отслеживание неполных взаимодействий с рекламой

## Новые файлы

- `api/logAdClick.php` - API для логирования кликов
- `test_ad_logging.html` - Тестовая страница
- `TESTING_GUIDE.md` - Это руководство

## Измененные файлы

- `js/ads-manager-full.js` - Добавлено логирование во все обработчики кликов
- `api/recordAdView.php` - Расширено определение стран и параметры логирования
- `api/admin/ad_stats_api.php` - Поддержка новых типов статусов
- `api/admin/ad_statistics.php` - Обновленный интерфейс статистики

## Новые типы статусов

| Статус | Описание |
|--------|----------|
| `button_click` | Клик по рекламной кнопке |
| `ad_request` | Запрос рекламы к сети |
| `no_ads_available` | Нет доступной рекламы |
| `ad_error` | Ошибка при показе рекламы |
| `limit_reached` | Достигнут дневной лимит |
| `ad_already_showing` | Реклама уже показывается |
| `cooldown_active` | Активен период ожидания |
| `ads_unavailable` | Реклама временно недоступна |

## Тестирование

### 1. Автоматическое тестирование

Откройте `test_ad_logging.html` в браузере:

```
http://argun-defolt.loc/test_ad_logging.html
```

Тестовая страница позволяет:
- Проверить все типы кликов
- Эмулировать различные сценарии ошибок
- Тестировать определение геолокации
- Просматривать логи в реальном времени

### 2. Ручное тестирование в мини-приложении

1. **Тест обычных кликов:**
   - Нажмите на любую рекламную кнопку
   - Проверьте, что клик залогирован как `button_click`

2. **Тест лимитов:**
   - Нажмите кнопку 20 раз подряд
   - 21-й клик должен логироваться как `limit_reached`

3. **Тест кулдауна:**
   - После просмотра рекламы нажмите кнопку снова
   - Клик должен логироваться как `cooldown_active`

### 3. Проверка в админ панели

Откройте админ панель статистики:

```
http://argun-defolt.loc/api/admin/ad_statistics.php
```

Проверьте:
- Новые карточки статистики (клики кнопок, запросы рекламы, нет рекламы)
- Фильтрацию по новым статусам
- Корректное отображение стран

## Проверка логов

### Структура лога

Каждая запись содержит:

```json
{
  "timestamp": 1234567890,
  "user_id": 123456789,
  "ad_type": "native_banner",
  "status": "button_click",
  "ip": "127.0.0.1",
  "user_agent": "Mozilla/5.0...",
  "country": "RU",
  "session_id": "session_123",
  "reason": "User clicked ad button",
  "additional_data": {
    "limits": {...},
    "view_id": "unique_view_id"
  }
}
```

### Файлы логов

- `database/ad_requests.json` - Основные логи
- `database/ad_click_logs.json` - Логи кликов (если отдельно)

## Ожидаемые результаты

После внедрения системы вы должны видеть:

1. **Увеличение количества записей** - теперь логируется каждый клик
2. **Детальную аналитику** - понимание, где пользователи "отваливаются"
3. **Географическую статистику** - корректное определение стран
4. **Полную картину взаимодействий** - от клика до награды

## Устранение неполадок

### Проблема: Не логируются клики

**Решение:**
1. Проверьте консоль браузера на ошибки JavaScript
2. Убедитесь, что `api/logAdClick.php` доступен
3. Проверьте права на запись в папку `database/`

### Проблема: Неправильное определение страны

**Решение:**
1. Проверьте функцию `getCountryByIP()` в `api/recordAdView.php`
2. Убедитесь, что IP-адрес передается корректно
3. Для тестирования используйте VPN или прокси

### Проблема: Статистика не обновляется

**Решение:**
1. Очистите кэш браузера
2. Проверьте, что `api/admin/ad_stats_api.php` возвращает данные
3. Убедитесь, что файлы логов не повреждены

## Мониторинг производительности

Следите за:
- Размером файлов логов (ротация при необходимости)
- Временем отклика API логирования
- Нагрузкой на сервер при частых запросах

## Следующие шаги

1. Мониторинг системы в течение недели
2. Анализ новых данных для оптимизации
3. Возможное добавление дополнительных метрик
4. Настройка автоматических отчетов
