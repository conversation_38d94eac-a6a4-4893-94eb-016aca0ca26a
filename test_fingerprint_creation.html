<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест создания отпечатков</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        button { padding: 10px 20px; margin: 5px; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🔍 Тест создания отпечатков устройств</h1>
    
    <div id="status" class="status info">Загрузка...</div>
    
    <button onclick="testBasicFingerprint()">Тест базового отпечатка</button>
    <button onclick="testAdvancedFingerprint()">Тест расширенного отпечатка</button>
    <button onclick="testServerRegistration()">Тест регистрации на сервере</button>
    <button onclick="checkCurrentFingerprints()">Проверить текущие отпечатки</button>
    
    <div id="results"></div>

    <!-- Подключаем необходимые скрипты -->
    <script src="js/device-fingerprint.js"></script>
    <script src="js/advanced-device-fingerprint.js"></script>
    <script src="js/vpn-detector.js"></script>
    <script src="js/self-referral-detector.js"></script>
    <script src="js/fraud-blocker.js"></script>

    <script>
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }

        function addResult(title, data) {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.innerHTML = `
                <h3>${title}</h3>
                <pre>${JSON.stringify(data, null, 2)}</pre>
            `;
            resultsDiv.appendChild(resultDiv);
        }

        async function testBasicFingerprint() {
            updateStatus('Создание базового отпечатка...', 'info');
            
            try {
                if (!window.DeviceFingerprint) {
                    throw new Error('DeviceFingerprint не загружен');
                }

                const fingerprint = new DeviceFingerprint();
                const result = await fingerprint.generateFingerprint();
                
                addResult('Базовый отпечаток', result);
                updateStatus('Базовый отпечаток создан успешно', 'success');
                
            } catch (error) {
                console.error('Ошибка создания базового отпечатка:', error);
                addResult('Ошибка базового отпечатка', { error: error.message });
                updateStatus('Ошибка создания базового отпечатка: ' + error.message, 'error');
            }
        }

        async function testAdvancedFingerprint() {
            updateStatus('Создание расширенного отпечатка...', 'info');
            
            try {
                if (!window.AdvancedDeviceFingerprint) {
                    throw new Error('AdvancedDeviceFingerprint не загружен');
                }

                const fingerprint = new AdvancedDeviceFingerprint();
                const result = await fingerprint.generateAdvancedFingerprint();
                
                addResult('Расширенный отпечаток', result);
                updateStatus('Расширенный отпечаток создан успешно', 'success');
                
            } catch (error) {
                console.error('Ошибка создания расширенного отпечатка:', error);
                addResult('Ошибка расширенного отпечатка', { error: error.message });
                updateStatus('Ошибка создания расширенного отпечатка: ' + error.message, 'error');
            }
        }

        async function testServerRegistration() {
            updateStatus('Тестирование регистрации на сервере...', 'info');
            
            try {
                // Создаем тестовый отпечаток
                const fingerprint = new DeviceFingerprint();
                const fingerprintData = await fingerprint.generateFingerprint();
                
                // Тестовые данные
                const testData = {
                    action: 'register_device',
                    initData: 'test_init_data',
                    fingerprint_data: fingerprintData,
                    vpn_data: { is_vpn_detected: false, risk_score: 0 }
                };

                const response = await fetch('/api/fraud-detection.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testData)
                });

                const result = await response.json();
                
                addResult('Регистрация на сервере', result);
                
                if (result.success) {
                    updateStatus('Отпечаток успешно зарегистрирован на сервере', 'success');
                } else {
                    updateStatus('Ошибка регистрации: ' + result.error, 'error');
                }
                
            } catch (error) {
                console.error('Ошибка регистрации на сервере:', error);
                addResult('Ошибка регистрации', { error: error.message });
                updateStatus('Ошибка регистрации на сервере: ' + error.message, 'error');
            }
        }

        async function checkCurrentFingerprints() {
            updateStatus('Проверка текущих отпечатков...', 'info');
            
            try {
                const response = await fetch('/api/fraud-detection.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'get_device_fingerprints',
                        page: 1,
                        limit: 10
                    })
                });

                const result = await response.json();
                
                addResult('Текущие отпечатки', result);
                
                if (result.success) {
                    updateStatus(`Найдено ${result.stats.total_fingerprints} отпечатков`, 'success');
                } else {
                    updateStatus('Ошибка получения отпечатков: ' + result.error, 'error');
                }
                
            } catch (error) {
                console.error('Ошибка получения отпечатков:', error);
                addResult('Ошибка получения отпечатков', { error: error.message });
                updateStatus('Ошибка получения отпечатков: ' + error.message, 'error');
            }
        }

        // Проверяем загрузку модулей при старте
        window.addEventListener('load', function() {
            const modules = [
                'DeviceFingerprint',
                'AdvancedDeviceFingerprint', 
                'VPNDetector',
                'SelfReferralDetector',
                'FraudBlocker'
            ];

            const loadedModules = modules.filter(module => window[module]);
            const missingModules = modules.filter(module => !window[module]);

            if (missingModules.length === 0) {
                updateStatus('Все модули загружены успешно', 'success');
            } else {
                updateStatus(`Не загружены модули: ${missingModules.join(', ')}`, 'error');
            }

            addResult('Статус модулей', {
                loaded: loadedModules,
                missing: missingModules
            });
        });
    </script>
</body>
</html>
