<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Финальный тест VPN блокировки</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-section {
            background: #2a2a2a;
            padding: 20px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .console {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #333;
            margin: 10px 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .danger { background: #dc3545; }
        .danger:hover { background: #c82333; }
        .success { background: #28a745; }
        .success:hover { background: #218838; }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 5px 0;
        }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
    </style>
</head>
<body>
    <h1>🔒 Финальный тест VPN блокировки</h1>
    
    <div class="test-section">
        <h3>🧪 Тестовые сценарии</h3>
        <button onclick="runFullTest()">🚀 Полный тест VPN блокировки</button>
        <button onclick="testWithoutVPN()" class="success">✅ Тест без VPN</button>
        <button onclick="testWithVPN()" class="danger">🚫 Тест с VPN</button>
        <button onclick="clearResults()">🧹 Очистить результаты</button>
    </div>

    <div class="test-section">
        <h3>📊 Результаты тестов</h3>
        <div id="results"></div>
    </div>

    <div class="test-section">
        <h3>🖥️ Консоль</h3>
        <div id="console" class="console">Готов к тестированию...\n</div>
    </div>

    <!-- Подключаем все модули -->
    <script src="js/device-fingerprint.js"></script>
    <script src="js/vpn-detector.js"></script>
    <script src="js/fraud-blocker.js"></script>
    <script src="js/fraud-manager.js"></script>

    <script>
        function log(message) {
            const console = document.getElementById('console');
            const timestamp = new Date().toLocaleTimeString();
            console.textContent += `[${timestamp}] ${message}\n`;
            console.scrollTop = console.scrollHeight;
            window.console.log(message);
        }

        function addResult(title, status, details) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `status ${status}`;
            div.innerHTML = `<strong>${title}</strong><br>${details}`;
            results.appendChild(div);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
            document.getElementById('console').textContent = 'Консоль очищена...\n';
        }

        async function runFullTest() {
            log('🚀 Запуск полного теста VPN блокировки...');
            clearResults();

            // Тест 1: Проверка загрузки модулей
            await testModuleLoading();
            
            // Тест 2: Тест без VPN
            await testWithoutVPN();
            
            // Тест 3: Тест с VPN
            await testWithVPN();
            
            log('✅ Полный тест завершен');
        }

        async function testModuleLoading() {
            log('📋 Тест 1: Проверка загрузки модулей...');
            
            const modules = [
                { name: 'DeviceFingerprint', obj: window.DeviceFingerprint },
                { name: 'VPNDetector', obj: window.VPNDetector },
                { name: 'FraudBlocker', obj: window.FraudBlocker },
                { name: 'FraudManager', obj: window.FraudManager },
                { name: 'fraudBlocker instance', obj: window.fraudBlocker }
            ];
            
            let allLoaded = true;
            let details = '';
            
            modules.forEach(module => {
                const loaded = !!module.obj;
                details += `${module.name}: ${loaded ? '✅' : '❌'}<br>`;
                if (!loaded) allLoaded = false;
                log(`${module.name}: ${loaded ? '✅' : '❌'}`);
            });
            
            addResult(
                'Загрузка модулей', 
                allLoaded ? 'success' : 'error',
                details
            );
            
            return allLoaded;
        }

        async function testWithoutVPN() {
            log('✅ Тест 2: Симуляция без VPN...');
            
            try {
                // Создаем мок VPNDetector без VPN
                const originalVPNDetector = window.VPNDetector;
                
                window.VPNDetector = class MockVPNDetectorNoVPN {
                    async detectVPN() {
                        log('🔍 Мок VPNDetector: VPN НЕ обнаружен');
                        return {
                            is_vpn_detected: false,
                            risk_score: 15,
                            indicators: [],
                            details: {}
                        };
                    }
                };
                
                const manager = new FraudManager();
                const testInitData = 'user=%7B%22id%22%3A123456789%2C%22first_name%22%3A%22TestUser%22%7D&auth_date=1234567890&hash=test';
                
                const result = await manager.initialize(testInitData);
                
                log(`Результат без VPN: ${result}`);
                log(`Пользователь заблокирован: ${manager.userBlocked}`);
                
                // Восстанавливаем оригинальный VPNDetector
                window.VPNDetector = originalVPNDetector;
                
                if (result && !manager.userBlocked) {
                    addResult(
                        'Тест без VPN', 
                        'success',
                        'Пользователь НЕ заблокирован (корректно)<br>Инициализация прошла успешно'
                    );
                    log('✅ Тест без VPN пройден');
                } else {
                    addResult(
                        'Тест без VPN', 
                        'error',
                        `Неожиданный результат:<br>result=${result}, blocked=${manager.userBlocked}`
                    );
                    log('❌ Тест без VPN не пройден');
                }
                
            } catch (error) {
                log(`❌ Ошибка в тесте без VPN: ${error.message}`);
                addResult('Тест без VPN', 'error', `Ошибка: ${error.message}`);
            }
        }

        async function testWithVPN() {
            log('🚫 Тест 3: Симуляция с VPN...');
            
            try {
                // Создаем мок VPNDetector с VPN
                const originalVPNDetector = window.VPNDetector;
                
                window.VPNDetector = class MockVPNDetectorWithVPN {
                    async detectVPN() {
                        log('🔍 Мок VPNDetector: VPN обнаружен!');
                        return {
                            is_vpn_detected: true,
                            risk_score: 85,
                            indicators: ['webrtc_leak', 'timing_anomaly', 'suspicious_dns'],
                            details: {
                                webrtc_ips: ['********', '***********'],
                                timing_score: 45,
                                dns_servers: ['*******', '*******']
                            }
                        };
                    }
                };
                
                const manager = new FraudManager();
                const testInitData = 'user=%7B%22id%22%3A123456789%2C%22first_name%22%3A%22TestUser%22%7D&auth_date=1234567890&hash=test';
                
                const result = await manager.initialize(testInitData);
                
                log(`Результат с VPN: ${result}`);
                log(`Пользователь заблокирован: ${manager.userBlocked}`);
                log(`VPN обнаружен: ${manager.vpnResults?.is_vpn_detected}`);
                
                // Восстанавливаем оригинальный VPNDetector
                window.VPNDetector = originalVPNDetector;
                
                if (!result && manager.userBlocked && manager.vpnResults?.is_vpn_detected) {
                    addResult(
                        'Тест с VPN', 
                        'success',
                        'Пользователь заблокирован (корректно)<br>VPN обнаружен и заблокирован<br>Экран блокировки показан'
                    );
                    log('✅ Тест с VPN пройден');
                    
                    // Убираем экран блокировки через 3 секунды
                    setTimeout(() => {
                        const screens = document.querySelectorAll('.fraud-blocking-screen');
                        screens.forEach(screen => screen.remove());
                        document.body.style.overflow = '';
                        log('🧹 Экран блокировки убран');
                    }, 3000);
                    
                } else {
                    addResult(
                        'Тест с VPN', 
                        'error',
                        `Неожиданный результат:<br>result=${result}, blocked=${manager.userBlocked}, vpn=${manager.vpnResults?.is_vpn_detected}`
                    );
                    log('❌ Тест с VPN не пройден');
                }
                
            } catch (error) {
                log(`❌ Ошибка в тесте с VPN: ${error.message}`);
                addResult('Тест с VPN', 'error', `Ошибка: ${error.message}`);
            }
        }

        // Автоматическая проверка при загрузке
        window.addEventListener('load', () => {
            setTimeout(() => {
                log('🚀 Страница загружена, готов к тестированию');
                testModuleLoading();
            }, 1000);
        });

        // Отслеживаем ошибки
        window.onerror = function(message, source, lineno, colno, error) {
            log(`🚨 JavaScript ошибка: ${message} в ${source}:${lineno}`);
            addResult('JavaScript ошибка', 'error', `${message} в ${source}:${lineno}`);
            return false;
        };
    </script>
</body>
</html>
