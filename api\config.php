<?php
// api/config.php

// !!! ПРАВИЛЬНЫЙ ТОКЕН БОТА @uniqpaid_paid_bot !!!
define('TELEGRAM_BOT_TOKEN', '**********************************************'); // <-- <PERSON><PERSON><PERSON><PERSON><PERSON> @uniqpaid_paid_bot

define('BOT_USERNAME', 'uniqpaid_paid_bot'); // <-- Имя пользователя вашего бота
define('WEBAPP_URL', 'https://app.uniqpaid.com/test3'); // <-- URL вашего Mini App

// Админские ID (исключения для всех блокировок)
define('ADMIN_USER_IDS', [5880288830]); // Ваш админский ID

define('USER_DATA_FILE', dirname(__DIR__) . '/api/user_data.json');
define('AD_VIEW_REWARD', 1);
define('REFERRAL_BONUS_PERCENT', 0.1); // 10% от награды реферала

// Награды за разные типы рекламы (проверяем, не определены ли уже)
if (!defined('AD_REWARD_NATIVE_BANNER')) {
    define('AD_REWARD_NATIVE_BANNER', 10); // Награда за баннер
}
if (!defined('AD_REWARD_INTERSTITIAL')) {
    define('AD_REWARD_INTERSTITIAL', 8); // Награда за полноэкранную рекламу
}
if (!defined('AD_REWARD_REWARDED_VIDEO')) {
    define('AD_REWARD_REWARDED_VIDEO', 2); // Награда за видеорекламу
}

// Лимиты успешных просмотров в день для одного пользователя (с начислением наград)
define('USER_AD_LIMIT_NATIVE_BANNER', 10); // Лимит для пользователя: баннеры в день
define('USER_AD_LIMIT_INTERSTITIAL', 10); // Лимит для пользователя: полноэкранная реклама в день
define('USER_AD_LIMIT_REWARDED_VIDEO', 20); // Лимит для пользователя: видеореклама в день

// Настройки для NOWPayments API
define('NOWPAYMENTS_API_KEY', '18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7'); // Приватный ключ для payout
define('NOWPAYMENTS_PUBLIC_KEY', 'f6627c2b-98ac-4d30-90dc-c01324330248'); // Публичный ключ для estimate
define('NOWPAYMENTS_IPN_SECRET', '+dtLfBgWRcW4ybhampqglG39/zxiGgwX'); // IPN секретный ключ
define('NOWPAYMENTS_API_URL', 'https://api.nowpayments.io/v1');
define('NOWPAYMENTS_TEST_MODE', false); // Включить тестовый режим

// Настройки логирования и безопасности
define('API_AUTH_ENABLED', true);
define('API_AUTH_TOKEN', 'secretnii-test'); // Установите свой токен

// Учетные данные для получения JWT токена
define('NOWPAYMENTS_EMAIL', '<EMAIL>'); // Email от аккаунта NOWPayments
define('NOWPAYMENTS_PASSWORD', 'Yjen10,er20'); // Пароль от аккаунта NOWPayments
define('MIN_WITHDRAWAL_AMOUNT', 100); // Нет минимальной суммы для вывода
define('MIN_BALANCE_FOR_WITHDRAWAL', 1); // Минимальный баланс для доступа к выводу (1 монета)
define('CONVERSION_RATE', 0.001); // Курс конвертации монет в USD (1 монета = 0.001 USD)
define('SHOW_FEES_TO_USER', true); // Показывать комиссии пользователю в калькуляторе

// Настройка валидации для продакшена
define('ALLOW_INVALID_HASH', false); // Строгая валидация для продакшена

// Настройки логирования ошибок PHP для продакшена
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/php-error.log');
error_reporting(E_ALL & ~E_NOTICE);
?>