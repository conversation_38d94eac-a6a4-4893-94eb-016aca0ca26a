{"27e938e4": {"fingerprint": "27e938e4", "user_id": 12345, "ip_address": "127.0.0.1", "timestamp": "2025-07-07 22:04:43", "created_at": 1751915083, "device_type": "desktop", "risk_score": 0, "components": {"canvas": "data:image/png;base64,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", "webgl": {"vendor": "WebKit", "renderer": "WebKit WebGL", "version": "WebGL 1.0 (OpenGL ES 2.0 Chromium)", "shadingLanguageVersion": "WebGL GLSL ES 1.0 (OpenGL ES GLSL ES 1.0 Chromium)", "unmaskedVendor": "Google Inc. (Google)", "unmaskedRenderer": "ANGLE (Google, Vulkan 1.3.0 (<PERSON><PERSON><PERSON><PERSON> (Subzero) (0x0000C0DE)), SwiftShader driver)", "extensions": "ANGLE_instanced_arrays,EXT_blend_minmax,EXT_clip_control,EXT_color_buffer_half_float,EXT_depth_clamp,EXT_float_blend,EXT_frag_depth,EXT_polygon_offset_clamp,EXT_shader_texture_lod,EXT_texture_compression_bptc,EXT_texture_compression_rgtc,EXT_texture_filter_anisotropic,EXT_texture_mirror_clamp_to_edge,EXT_sRGB,OES_element_index_uint,OES_fbo_render_mipmap,OES_standard_derivatives,OES_texture_float,OES_texture_float_linear,OES_texture_half_float,OES_texture_half_float_linear,OES_vertex_array_object,WEBGL_color_buffer_float,WEBGL_compressed_texture_astc,WEBGL_compressed_texture_etc,WEBGL_compressed_texture_etc1,WEBGL_compressed_texture_s3tc,WEBGL_compressed_texture_s3tc_srgb,WEBGL_debug_renderer_info,WEBGL_debug_shaders,WEBGL_depth_texture,WEBGL_draw_buffers,WEBGL_lose_context,WEBGL_multi_draw,WEBGL_polygon_mode", "maxTextureSize": 8192, "maxViewportDims": "8192,8192"}, "audio": "audio_timeout", "screen": {"width": 1366, "height": 768, "availWidth": 1366, "availHeight": 768, "colorDepth": 24, "pixelDepth": 24, "orientation": "landscape-primary", "devicePixelRatio": 1, "innerWidth": 1366, "innerHeight": 690, "outerWidth": 1366, "outerHeight": 768}, "system": {"platform": "Win32", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* YaBrowser/******** Safari/537.36", "language": "ru", "languages": "ru,en", "hardwareConcurrency": 4, "deviceMemory": "unknown", "maxTouchPoints": 0, "cookieEnabled": true, "doNotTrack": "1", "onLine": true}, "navigator": {"plugins": "PDF Viewer,Chrome PDF Viewer,Chromium PDF Viewer,Microsoft Edge PDF Viewer,WebKit built-in PDF", "mimeTypes": "application/pdf,text/pdf", "vendor": "Google Inc.", "product": "Gecko", "productSub": "20030107", "appName": "Netscape", "appVersion": "5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* YaBrowser/******** Safari/537.36"}, "timezone": {"timezone": "Europe/Moscow", "timezoneOffset": -180, "locale": "ru", "calendar": "gregory", "numberingSystem": "latn"}, "fonts": "Arial,Helvetica,Times New Roman,Courier New,Verdana,Georgia,Comic Sans MS,Trebuchet MS,Arial Black,Impact,Tahoma,Calibri"}}}