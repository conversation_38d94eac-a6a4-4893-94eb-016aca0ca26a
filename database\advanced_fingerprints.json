{"666012a": {"fingerprint": "666012a", "components": {"canvas": "data:image/png;base64,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", "webgl": {"vendor": "WebKit", "renderer": "WebKit WebGL", "version": "WebGL 1.0 (OpenGL ES 2.0 Chromium)", "shadingLanguageVersion": "WebGL GLSL ES 1.0 (OpenGL ES GLSL ES 1.0 Chromium)", "unmaskedVendor": "Google Inc. (Google)", "unmaskedRenderer": "ANGLE (Google, Vulkan 1.3.0 (<PERSON><PERSON><PERSON><PERSON> (Subzero) (0x0000C0DE)), SwiftShader driver)", "extensions": "ANGLE_instanced_arrays,EXT_blend_minmax,EXT_clip_control,EXT_color_buffer_half_float,EXT_depth_clamp,EXT_float_blend,EXT_frag_depth,EXT_polygon_offset_clamp,EXT_shader_texture_lod,EXT_texture_compression_bptc,EXT_texture_compression_rgtc,EXT_texture_filter_anisotropic,EXT_texture_mirror_clamp_to_edge,EXT_sRGB,OES_element_index_uint,OES_fbo_render_mipmap,OES_standard_derivatives,OES_texture_float,OES_texture_float_linear,OES_texture_half_float,OES_texture_half_float_linear,OES_vertex_array_object,WEBGL_color_buffer_float,WEBGL_compressed_texture_astc,WEBGL_compressed_texture_etc,WEBGL_compressed_texture_etc1,WEBGL_compressed_texture_s3tc,WEBGL_compressed_texture_s3tc_srgb,WEBGL_debug_renderer_info,WEBGL_debug_shaders,WEBGL_depth_texture,WEBGL_draw_buffers,WEBGL_lose_context,WEBGL_multi_draw,WEBGL_polygon_mode", "maxTextureSize": 8192, "maxViewportDims": "8192,8192"}, "audio": "0,0.08264365047216415,0.16771934926509857,0.2492087185382843,0.3338141441345215,0.4166487157344818,0.5001381635665894,0.5834451913833618,0.6663544178009033,0.7517519593238831,0.8300729393959045,0.9228833317756653,0.9787084460258484,0.9228832721710205,0.8300728797912598,0.7517519593238831,0.6663542985916138,0.583445131778717,0.5001381635665894,0.41664862632751465,0.3338140547275543,0.2492087185382843,0.1677192896604538,0.08264358341693878,0,-0.08264380693435669,-0.16771942377090454,-0.2492087185382843,-0.3338142931461334,-0.4166487753391266,-0.5001381635665894,-0.5834453105926514,-0.6663544774055481,-0.7517519593238831,-0.8300730586051941,-0.9228834509849548,-0.9787084460258484,-0.9228830933570862,-0.8300728797912598,-0.7517519593238831,-0.6663541793823242,-0.583445131778717,-0.5001381635665894,-0.4166485071182251,-0.3338140547275543,-0.2492087185382843,-0.16771917045116425,-0.08264358341693878,1.1521713361162256e-7,0.0826437696814537,0.16771946847438812,0.24920883774757385,0.33381426334381104,0.41664883494377136,0.5001382827758789,0.5834453105926514,0.6663545370101929,0.7517520785331726,0.8300730586051941,0.9228835105895996,0.9787084460258484,0.9228830933570862,0.8300727605819702,0.7517518401145935,0.6663541793823242,0.5834450125694275,0.5001380443572998,0.4166485071182251,0.33381393551826477,0.24920859932899475,0.16771917045116425,0.08264345675706863,-2.3043426722324512e-7,-0.08264380693435669,-0.16771942377090454,-0.2492089569568634,-0.3338142931461334,-0.4166487753391266,-0.5001384019851685,-0.5834453105926514,-0.6663547158241272,-0.7517521977424622,-0.8300730586051941,-0.9228836894035339,-0.9787084460258484,-0.9228830933570862,-0.8300726413726807,-0.751751720905304,-0.6663541793823242,-0.5834448933601379,-0.5001379251480103,-0.4166485071182251,-0.3338138163089752,-0.2492084950208664,-0.16771917045116425,-0.08264333754777908,2.3043426722324512e-7,0.08264389634132385,0.16771958768367767,0.2492089569568634", "screen": {"width": 1366, "height": 768, "availWidth": 1366, "availHeight": 768, "colorDepth": 24, "pixelDepth": 24, "orientation": "landscape-primary", "devicePixelRatio": 1, "innerWidth": 1366, "innerHeight": 690, "outerWidth": 1366, "outerHeight": 768}, "system": {"platform": "Win32", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 YaBrowser/25.6.0.0 Safari/537.36", "language": "ru", "languages": "ru,en", "hardwareConcurrency": 4, "deviceMemory": "unknown", "maxTouchPoints": 0, "cookieEnabled": true, "doNotTrack": "1", "onLine": true}, "navigator": {"plugins": "PDF Viewer,Chrome PDF Viewer,Chromium PDF Viewer,Microsoft Edge PDF Viewer,WebKit built-in PDF", "mimeTypes": "application/pdf,text/pdf", "vendor": "Google Inc.", "product": "Gecko", "productSub": "20030107", "appName": "Netscape", "appVersion": "5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 YaBrowser/25.6.0.0 Safari/537.36"}, "timezone": {"timezone": "Europe/Moscow", "timezoneOffset": -180, "locale": "ru", "calendar": "gregory", "numberingSystem": "latn"}, "fonts": "Arial,Helvetica,Times New Roman,Courier New,Verdana,Georgia,Comic Sans MS,Trebuchet MS,Arial Black,Impact,Tahoma,Calibri", "hardware": {"hardwareConcurrency": 4, "deviceMemory": "unknown", "gpu": {"vendor": "WebKit", "renderer": "WebKit WebGL", "unmaskedVendor": "Google Inc. (Google)", "unmaskedRenderer": "ANGLE (Google, Vulkan 1.3.0 (<PERSON><PERSON><PERSON><PERSON> (Subzero) (0x0000C0DE)), SwiftShader driver)"}, "maxTouchPoints": 0, "vibration": true, "gamepad": true, "mediaDevices": false, "getUserMedia": false, "connection": {"effectiveType": "2g", "downlink": 0.25, "rtt": 1900, "saveData": false}, "storageQuota": "storage_quota_unavailable"}, "performance": {"jsPerformance": 2071, "canvasPerformance": 4, "webglPerformance": 0, "timing": {"navigationStart": 1751831475627, "loadEventEnd": 1751831475771, "domContentLoaded": 143, "loadComplete": 144}, "memory": {"usedJSHeapSize": 10831255, "totalJSHeapSize": 12546839, "jsHeapSizeLimit": 1953759232}}, "webrtc": {"localIPs": [], "candidatesCount": 0, "webrtcSupport": true}, "battery": "battery_unavailable", "sensors": {"accelerometer": false, "gyroscope": false, "magnetometer": false, "ambientLight": false, "proximity": false, "deviceOrientation": false, "deviceMotion": false}, "media": "media_unavailable", "storage": {"localStorage": true, "sessionStorage": true, "indexedDB": true, "webSQL": false, "cookies": true}, "network": {"onLine": true, "connection": {"effectiveType": "2g", "downlink": 0.25, "rtt": 1900, "saveData": false}, "effectiveType": "2g", "downlink": 0.25, "rtt": 1900}, "emulatorDetection": {"indicators": [], "riskPoints": 0, "isEmulator": false}, "botDetection": {"indicators": [], "riskPoints": 0, "isBot": false}, "spoofingDetection": {"indicators": [], "riskPoints": 0, "isSpoofed": false}, "behavioral": {"sessionDuration": 1228, "mouseEventsCount": 1, "keyboardEventsCount": 0, "scrollEventsCount": 6, "mousePattern": "insufficient_data", "typingPattern": "insufficient_data"}}, "risk_score": 0, "risk_assessment": {"totalRiskScore": 0, "riskFactors": [{"type": "device_fingerprint", "score": 0, "indicators": []}], "recommendations": ["normal_access"], "blockingReasons": []}, "user_id": "12345", "ip_address": "127.0.0.1", "timestamp": "2025-07-06 22:51:27", "created_at": 1751831487}, "28139ea5": {"fingerprint": "28139ea5", "components": {"canvas": "data:image/png;base64,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", "webgl": {"vendor": "WebKit", "renderer": "WebKit WebGL", "version": "WebGL 1.0 (OpenGL ES 2.0 Chromium)", "shadingLanguageVersion": "WebGL GLSL ES 1.0 (OpenGL ES GLSL ES 1.0 Chromium)", "unmaskedVendor": "Google Inc. (Google)", "unmaskedRenderer": "ANGLE (Google, Vulkan 1.3.0 (<PERSON><PERSON><PERSON><PERSON> (Subzero) (0x0000C0DE)), SwiftShader driver)", "extensions": "ANGLE_instanced_arrays,EXT_blend_minmax,EXT_clip_control,EXT_color_buffer_half_float,EXT_depth_clamp,EXT_float_blend,EXT_frag_depth,EXT_polygon_offset_clamp,EXT_shader_texture_lod,EXT_texture_compression_bptc,EXT_texture_compression_rgtc,EXT_texture_filter_anisotropic,EXT_texture_mirror_clamp_to_edge,EXT_sRGB,OES_element_index_uint,OES_fbo_render_mipmap,OES_standard_derivatives,OES_texture_float,OES_texture_float_linear,OES_texture_half_float,OES_texture_half_float_linear,OES_vertex_array_object,WEBGL_color_buffer_float,WEBGL_compressed_texture_astc,WEBGL_compressed_texture_etc,WEBGL_compressed_texture_etc1,WEBGL_compressed_texture_s3tc,WEBGL_compressed_texture_s3tc_srgb,WEBGL_debug_renderer_info,WEBGL_debug_shaders,WEBGL_depth_texture,WEBGL_draw_buffers,WEBGL_lose_context,WEBGL_multi_draw,WEBGL_polygon_mode", "maxTextureSize": 8192, "maxViewportDims": "8192,8192"}, "audio": "0,0.08264365047216415,0.16771934926509857,0.2492087185382843,0.3338141441345215,0.4166487157344818,0.5001381635665894,0.5834451913833618,0.6663544178009033,0.7517519593238831,0.8300729393959045,0.9228833317756653,0.9787084460258484,0.9228832721710205,0.8300728797912598,0.7517519593238831,0.6663542985916138,0.583445131778717,0.5001381635665894,0.41664862632751465,0.3338140547275543,0.2492087185382843,0.1677192896604538,0.08264358341693878,0,-0.08264380693435669,-0.16771942377090454,-0.2492087185382843,-0.3338142931461334,-0.4166487753391266,-0.5001381635665894,-0.5834453105926514,-0.6663544774055481,-0.7517519593238831,-0.8300730586051941,-0.9228834509849548,-0.9787084460258484,-0.9228830933570862,-0.8300728797912598,-0.7517519593238831,-0.6663541793823242,-0.583445131778717,-0.5001381635665894,-0.4166485071182251,-0.3338140547275543,-0.2492087185382843,-0.16771917045116425,-0.08264358341693878,1.1521713361162256e-7,0.0826437696814537,0.16771946847438812,0.24920883774757385,0.33381426334381104,0.41664883494377136,0.5001382827758789,0.5834453105926514,0.6663545370101929,0.7517520785331726,0.8300730586051941,0.9228835105895996,0.9787084460258484,0.9228830933570862,0.8300727605819702,0.7517518401145935,0.6663541793823242,0.5834450125694275,0.5001380443572998,0.4166485071182251,0.33381393551826477,0.24920859932899475,0.16771917045116425,0.08264345675706863,-2.3043426722324512e-7,-0.08264380693435669,-0.16771942377090454,-0.2492089569568634,-0.3338142931461334,-0.4166487753391266,-0.5001384019851685,-0.5834453105926514,-0.6663547158241272,-0.7517521977424622,-0.8300730586051941,-0.9228836894035339,-0.9787084460258484,-0.9228830933570862,-0.8300726413726807,-0.751751720905304,-0.6663541793823242,-0.5834448933601379,-0.5001379251480103,-0.4166485071182251,-0.3338138163089752,-0.2492084950208664,-0.16771917045116425,-0.08264333754777908,2.3043426722324512e-7,0.08264389634132385,0.16771958768367767,0.2492089569568634", "screen": {"width": 1366, "height": 768, "availWidth": 1366, "availHeight": 768, "colorDepth": 24, "pixelDepth": 24, "orientation": "landscape-primary", "devicePixelRatio": 1, "innerWidth": 1366, "innerHeight": 690, "outerWidth": 1366, "outerHeight": 768}, "system": {"platform": "Win32", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 YaBrowser/25.6.0.0 Safari/537.36", "language": "ru", "languages": "ru,en", "hardwareConcurrency": 4, "deviceMemory": "unknown", "maxTouchPoints": 0, "cookieEnabled": true, "doNotTrack": "1", "onLine": true}, "navigator": {"plugins": "PDF Viewer,Chrome PDF Viewer,Chromium PDF Viewer,Microsoft Edge PDF Viewer,WebKit built-in PDF", "mimeTypes": "application/pdf,text/pdf", "vendor": "Google Inc.", "product": "Gecko", "productSub": "20030107", "appName": "Netscape", "appVersion": "5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 YaBrowser/25.6.0.0 Safari/537.36"}, "timezone": {"timezone": "Europe/Moscow", "timezoneOffset": -180, "locale": "ru", "calendar": "gregory", "numberingSystem": "latn"}, "fonts": "Arial,Helvetica,Times New Roman,Courier New,Verdana,Georgia,Comic Sans MS,Trebuchet MS,Arial Black,Impact,Tahoma,Calibri", "hardware": {"hardwareConcurrency": 4, "deviceMemory": "unknown", "gpu": {"vendor": "WebKit", "renderer": "WebKit WebGL", "unmaskedVendor": "Google Inc. (Google)", "unmaskedRenderer": "ANGLE (Google, Vulkan 1.3.0 (<PERSON><PERSON><PERSON><PERSON> (Subzero) (0x0000C0DE)), SwiftShader driver)"}, "maxTouchPoints": 0, "vibration": true, "gamepad": true, "mediaDevices": false, "getUserMedia": false, "connection": {"effectiveType": "2g", "downlink": 0.25, "rtt": 1900, "saveData": false}, "storageQuota": "storage_quota_unavailable"}, "performance": {"jsPerformance": 1744, "canvasPerformance": 3, "webglPerformance": 1, "timing": {"navigationStart": 1751831475627, "loadEventEnd": 1751831475771, "domContentLoaded": 143, "loadComplete": 144}, "memory": {"usedJSHeapSize": 10571587, "totalJSHeapSize": 13220251, "jsHeapSizeLimit": 1953759232}}, "webrtc": {"localIPs": [], "candidatesCount": 0, "webrtcSupport": true}, "battery": "battery_unavailable", "sensors": {"accelerometer": false, "gyroscope": false, "magnetometer": false, "ambientLight": false, "proximity": false, "deviceOrientation": false, "deviceMotion": false}, "media": "media_unavailable", "storage": {"localStorage": true, "sessionStorage": true, "indexedDB": true, "webSQL": false, "cookies": true}, "network": {"onLine": true, "connection": {"effectiveType": "2g", "downlink": 0.25, "rtt": 1900, "saveData": false}, "effectiveType": "2g", "downlink": 0.25, "rtt": 1900}, "emulatorDetection": {"indicators": [], "riskPoints": 0, "isEmulator": false}, "botDetection": {"indicators": [], "riskPoints": 0, "isBot": false}, "spoofingDetection": {"indicators": [], "riskPoints": 0, "isSpoofed": false}, "behavioral": {"sessionDuration": 1514, "mouseEventsCount": 17, "keyboardEventsCount": 0, "scrollEventsCount": 10, "mousePattern": {"averageSpeed": 0.27332716799164, "totalDistance": 141.5834730196695, "totalTime": 518, "eventsCount": 17}, "typingPattern": "insufficient_data"}}, "risk_score": 0, "risk_assessment": {"riskFactors": [{"type": "test_factor", "score": 10}], "recommendations": ["test_recommendation"]}, "user_id": "12345", "ip_address": "127.0.0.1", "timestamp": "2025-07-06 22:51:27", "created_at": 1751831487}, "test_fingerprint_1751831695": {"fingerprint": "test_fingerprint_1751831695", "components": {"canvas": "test_canvas_hash", "webgl": "test_webgl_hash", "audio": "test_audio_hash"}, "risk_score": 15, "risk_assessment": {"riskFactors": [{"type": "test_factor", "score": 10}], "recommendations": ["test_recommendation"]}, "user_id": "12345", "ip_address": "127.0.0.1", "timestamp": "2025-07-06 19:54:55", "created_at": 1751831695}, "26cd290": {"fingerprint": "26cd290", "components": {"canvas": "data:image/png;base64,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", "webgl": {"vendor": "WebKit", "renderer": "WebKit WebGL", "version": "WebGL 1.0 (OpenGL ES 2.0 Chromium)", "shadingLanguageVersion": "WebGL GLSL ES 1.0 (OpenGL ES GLSL ES 1.0 Chromium)", "unmaskedVendor": "Google Inc. (Google)", "unmaskedRenderer": "ANGLE (Google, Vulkan 1.3.0 (<PERSON><PERSON><PERSON><PERSON> (Subzero) (0x0000C0DE)), SwiftShader driver)", "extensions": "ANGLE_instanced_arrays,EXT_blend_minmax,EXT_clip_control,EXT_color_buffer_half_float,EXT_depth_clamp,EXT_float_blend,EXT_frag_depth,EXT_polygon_offset_clamp,EXT_shader_texture_lod,EXT_texture_compression_bptc,EXT_texture_compression_rgtc,EXT_texture_filter_anisotropic,EXT_texture_mirror_clamp_to_edge,EXT_sRGB,OES_element_index_uint,OES_fbo_render_mipmap,OES_standard_derivatives,OES_texture_float,OES_texture_float_linear,OES_texture_half_float,OES_texture_half_float_linear,OES_vertex_array_object,WEBGL_color_buffer_float,WEBGL_compressed_texture_astc,WEBGL_compressed_texture_etc,WEBGL_compressed_texture_etc1,WEBGL_compressed_texture_s3tc,WEBGL_compressed_texture_s3tc_srgb,WEBGL_debug_renderer_info,WEBGL_debug_shaders,WEBGL_depth_texture,WEBGL_draw_buffers,WEBGL_lose_context,WEBGL_multi_draw,WEBGL_polygon_mode", "maxTextureSize": 8192, "maxViewportDims": "8192,8192"}, "audio": "0,0.08264365047216415,0.16771934926509857,0.2492087185382843,0.3338141441345215,0.4166487157344818,0.5001381635665894,0.5834451913833618,0.6663544178009033,0.7517519593238831,0.8300729393959045,0.9228833317756653,0.9787084460258484,0.9228832721710205,0.8300728797912598,0.7517519593238831,0.6663542985916138,0.583445131778717,0.5001381635665894,0.41664862632751465,0.3338140547275543,0.2492087185382843,0.1677192896604538,0.08264358341693878,0,-0.08264380693435669,-0.16771942377090454,-0.2492087185382843,-0.3338142931461334,-0.4166487753391266,-0.5001381635665894,-0.5834453105926514,-0.6663544774055481,-0.7517519593238831,-0.8300730586051941,-0.9228834509849548,-0.9787084460258484,-0.9228830933570862,-0.8300728797912598,-0.7517519593238831,-0.6663541793823242,-0.583445131778717,-0.5001381635665894,-0.4166485071182251,-0.3338140547275543,-0.2492087185382843,-0.16771917045116425,-0.08264358341693878,1.1521713361162256e-7,0.0826437696814537,0.16771946847438812,0.24920883774757385,0.33381426334381104,0.41664883494377136,0.5001382827758789,0.5834453105926514,0.6663545370101929,0.7517520785331726,0.8300730586051941,0.9228835105895996,0.9787084460258484,0.9228830933570862,0.8300727605819702,0.7517518401145935,0.6663541793823242,0.5834450125694275,0.5001380443572998,0.4166485071182251,0.33381393551826477,0.24920859932899475,0.16771917045116425,0.08264345675706863,-2.3043426722324512e-7,-0.08264380693435669,-0.16771942377090454,-0.2492089569568634,-0.3338142931461334,-0.4166487753391266,-0.5001384019851685,-0.5834453105926514,-0.6663547158241272,-0.7517521977424622,-0.8300730586051941,-0.9228836894035339,-0.9787084460258484,-0.9228830933570862,-0.8300726413726807,-0.751751720905304,-0.6663541793823242,-0.5834448933601379,-0.5001379251480103,-0.4166485071182251,-0.3338138163089752,-0.2492084950208664,-0.16771917045116425,-0.08264333754777908,2.3043426722324512e-7,0.08264389634132385,0.16771958768367767,0.2492089569568634", "screen": {"width": 1366, "height": 768, "availWidth": 1366, "availHeight": 768, "colorDepth": 24, "pixelDepth": 24, "orientation": "landscape-primary", "devicePixelRatio": 1, "innerWidth": 1366, "innerHeight": 496, "outerWidth": 1366, "outerHeight": 768}, "system": {"platform": "Win32", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 YaBrowser/25.6.0.0 Safari/537.36", "language": "ru", "languages": "ru,en", "hardwareConcurrency": 4, "deviceMemory": "unknown", "maxTouchPoints": 0, "cookieEnabled": true, "doNotTrack": "1", "onLine": true}, "navigator": {"plugins": "PDF Viewer,Chrome PDF Viewer,Chromium PDF Viewer,Microsoft Edge PDF Viewer,WebKit built-in PDF", "mimeTypes": "application/pdf,text/pdf", "vendor": "Google Inc.", "product": "Gecko", "productSub": "20030107", "appName": "Netscape", "appVersion": "5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 YaBrowser/25.6.0.0 Safari/537.36"}, "timezone": {"timezone": "Europe/Moscow", "timezoneOffset": -180, "locale": "ru", "calendar": "gregory", "numberingSystem": "latn"}, "fonts": "Arial,Helvetica,Times New Roman,Courier New,Verdana,Georgia,Comic Sans MS,Trebuchet MS,Arial Black,Impact,Tahoma,Calibri", "hardware": {"hardwareConcurrency": 4, "deviceMemory": "unknown", "gpu": {"vendor": "WebKit", "renderer": "WebKit WebGL", "unmaskedVendor": "Google Inc. (Google)", "unmaskedRenderer": "ANGLE (Google, Vulkan 1.3.0 (<PERSON><PERSON><PERSON><PERSON> (Subzero) (0x0000C0DE)), SwiftShader driver)"}, "maxTouchPoints": 0, "vibration": true, "gamepad": true, "mediaDevices": false, "getUserMedia": false, "connection": {"effectiveType": "2g", "downlink": 0.25, "rtt": 1900, "saveData": false}, "storageQuota": "storage_quota_unavailable"}, "performance": {"jsPerformance": 795, "canvasPerformance": 2, "webglPerformance": 1, "timing": {"navigationStart": 1751831737107, "loadEventEnd": 1751831737458, "domContentLoaded": 350, "loadComplete": 351}, "memory": {"usedJSHeapSize": 8612452, "totalJSHeapSize": 11132668, "jsHeapSizeLimit": 1953759232}}, "webrtc": {"localIPs": [], "candidatesCount": 0, "webrtcSupport": true}, "battery": "battery_unavailable", "sensors": {"accelerometer": false, "gyroscope": false, "magnetometer": false, "ambientLight": false, "proximity": false, "deviceOrientation": false, "deviceMotion": false}, "media": "media_unavailable", "storage": {"localStorage": true, "sessionStorage": true, "indexedDB": true, "webSQL": false, "cookies": true}, "network": {"onLine": true, "connection": {"effectiveType": "2g", "downlink": 0.25, "rtt": 1900, "saveData": false}, "effectiveType": "2g", "downlink": 0.25, "rtt": 1900}, "emulatorDetection": {"indicators": [], "riskPoints": 0, "isEmulator": false}, "botDetection": {"indicators": [], "riskPoints": 0, "isBot": false}, "spoofingDetection": {"indicators": [], "riskPoints": 0, "isSpoofed": false}, "behavioral": {"sessionDuration": 1534, "mouseEventsCount": 21, "keyboardEventsCount": 0, "scrollEventsCount": 0, "mousePattern": {"averageSpeed": 1.3411443887667958, "totalDistance": 282.9814660297939, "totalTime": 211, "eventsCount": 21}, "typingPattern": "insufficient_data"}}, "risk_score": 0, "risk_assessment": {"totalRiskScore": 10, "riskFactors": [{"type": "device_fingerprint", "score": 0, "indicators": []}, {"type": "devtools_detected", "score": 10}], "recommendations": ["normal_access"], "blockingReasons": []}, "user_id": "12345", "ip_address": "127.0.0.1", "timestamp": "2025-07-06 22:55:59", "created_at": 1751831759}, "321827ca": {"fingerprint": "321827ca", "components": {"canvas": "data:image/png;base64,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", "webgl": {"vendor": "WebKit", "renderer": "WebKit WebGL", "version": "WebGL 1.0 (OpenGL ES 2.0 Chromium)", "shadingLanguageVersion": "WebGL GLSL ES 1.0 (OpenGL ES GLSL ES 1.0 Chromium)", "unmaskedVendor": "Google Inc. (Google)", "unmaskedRenderer": "ANGLE (Google, Vulkan 1.3.0 (<PERSON><PERSON><PERSON><PERSON> (Subzero) (0x0000C0DE)), SwiftShader driver)", "extensions": "ANGLE_instanced_arrays,EXT_blend_minmax,EXT_clip_control,EXT_color_buffer_half_float,EXT_depth_clamp,EXT_float_blend,EXT_frag_depth,EXT_polygon_offset_clamp,EXT_shader_texture_lod,EXT_texture_compression_bptc,EXT_texture_compression_rgtc,EXT_texture_filter_anisotropic,EXT_texture_mirror_clamp_to_edge,EXT_sRGB,OES_element_index_uint,OES_fbo_render_mipmap,OES_standard_derivatives,OES_texture_float,OES_texture_float_linear,OES_texture_half_float,OES_texture_half_float_linear,OES_vertex_array_object,WEBGL_color_buffer_float,WEBGL_compressed_texture_astc,WEBGL_compressed_texture_etc,WEBGL_compressed_texture_etc1,WEBGL_compressed_texture_s3tc,WEBGL_compressed_texture_s3tc_srgb,WEBGL_debug_renderer_info,WEBGL_debug_shaders,WEBGL_depth_texture,WEBGL_draw_buffers,WEBGL_lose_context,WEBGL_multi_draw,WEBGL_polygon_mode", "maxTextureSize": 8192, "maxViewportDims": "8192,8192"}, "audio": "0,0.08264365047216415,0.16771934926509857,0.2492087185382843,0.3338141441345215,0.4166487157344818,0.5001381635665894,0.5834451913833618,0.6663544178009033,0.7517519593238831,0.8300729393959045,0.9228833317756653,0.9787084460258484,0.9228832721710205,0.8300728797912598,0.7517519593238831,0.6663542985916138,0.583445131778717,0.5001381635665894,0.41664862632751465,0.3338140547275543,0.2492087185382843,0.1677192896604538,0.08264358341693878,0,-0.08264380693435669,-0.16771942377090454,-0.2492087185382843,-0.3338142931461334,-0.4166487753391266,-0.5001381635665894,-0.5834453105926514,-0.6663544774055481,-0.7517519593238831,-0.8300730586051941,-0.9228834509849548,-0.9787084460258484,-0.9228830933570862,-0.8300728797912598,-0.7517519593238831,-0.6663541793823242,-0.583445131778717,-0.5001381635665894,-0.4166485071182251,-0.3338140547275543,-0.2492087185382843,-0.16771917045116425,-0.08264358341693878,1.1521713361162256e-7,0.0826437696814537,0.16771946847438812,0.24920883774757385,0.33381426334381104,0.41664883494377136,0.5001382827758789,0.5834453105926514,0.6663545370101929,0.7517520785331726,0.8300730586051941,0.9228835105895996,0.9787084460258484,0.9228830933570862,0.8300727605819702,0.7517518401145935,0.6663541793823242,0.5834450125694275,0.5001380443572998,0.4166485071182251,0.33381393551826477,0.24920859932899475,0.16771917045116425,0.08264345675706863,-2.3043426722324512e-7,-0.08264380693435669,-0.16771942377090454,-0.2492089569568634,-0.3338142931461334,-0.4166487753391266,-0.5001384019851685,-0.5834453105926514,-0.6663547158241272,-0.7517521977424622,-0.8300730586051941,-0.9228836894035339,-0.9787084460258484,-0.9228830933570862,-0.8300726413726807,-0.751751720905304,-0.6663541793823242,-0.5834448933601379,-0.5001379251480103,-0.4166485071182251,-0.3338138163089752,-0.2492084950208664,-0.16771917045116425,-0.08264333754777908,2.3043426722324512e-7,0.08264389634132385,0.16771958768367767,0.2492089569568634", "screen": {"width": 1366, "height": 768, "availWidth": 1366, "availHeight": 768, "colorDepth": 24, "pixelDepth": 24, "orientation": "landscape-primary", "devicePixelRatio": 1, "innerWidth": 1366, "innerHeight": 496, "outerWidth": 1366, "outerHeight": 768}, "system": {"platform": "Win32", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 YaBrowser/25.6.0.0 Safari/537.36", "language": "ru", "languages": "ru,en", "hardwareConcurrency": 4, "deviceMemory": "unknown", "maxTouchPoints": 0, "cookieEnabled": true, "doNotTrack": "1", "onLine": true}, "navigator": {"plugins": "PDF Viewer,Chrome PDF Viewer,Chromium PDF Viewer,Microsoft Edge PDF Viewer,WebKit built-in PDF", "mimeTypes": "application/pdf,text/pdf", "vendor": "Google Inc.", "product": "Gecko", "productSub": "20030107", "appName": "Netscape", "appVersion": "5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 YaBrowser/25.6.0.0 Safari/537.36"}, "timezone": {"timezone": "Europe/Moscow", "timezoneOffset": -180, "locale": "ru", "calendar": "gregory", "numberingSystem": "latn"}, "fonts": "Arial,Helvetica,Times New Roman,Courier New,Verdana,Georgia,Comic Sans MS,Trebuchet MS,Arial Black,Impact,Tahoma,Calibri", "hardware": {"hardwareConcurrency": 4, "deviceMemory": "unknown", "gpu": {"vendor": "WebKit", "renderer": "WebKit WebGL", "unmaskedVendor": "Google Inc. (Google)", "unmaskedRenderer": "ANGLE (Google, Vulkan 1.3.0 (<PERSON><PERSON><PERSON><PERSON> (Subzero) (0x0000C0DE)), SwiftShader driver)"}, "maxTouchPoints": 0, "vibration": true, "gamepad": true, "mediaDevices": false, "getUserMedia": false, "connection": {"effectiveType": "2g", "downlink": 0.25, "rtt": 1900, "saveData": false}, "storageQuota": "storage_quota_unavailable"}, "performance": {"jsPerformance": 2622, "canvasPerformance": 3, "webglPerformance": 2, "timing": {"navigationStart": 1751831737107, "loadEventEnd": 1751831737458, "domContentLoaded": 350, "loadComplete": 351}, "memory": {"usedJSHeapSize": 10062098, "totalJSHeapSize": 11290534, "jsHeapSizeLimit": 1953759232}}, "webrtc": {"localIPs": [], "candidatesCount": 0, "webrtcSupport": true}, "battery": "battery_unavailable", "sensors": {"accelerometer": false, "gyroscope": false, "magnetometer": false, "ambientLight": false, "proximity": false, "deviceOrientation": false, "deviceMotion": false}, "media": "media_unavailable", "storage": {"localStorage": true, "sessionStorage": true, "indexedDB": true, "webSQL": false, "cookies": true}, "network": {"onLine": true, "connection": {"effectiveType": "2g", "downlink": 0.25, "rtt": 1900, "saveData": false}, "effectiveType": "2g", "downlink": 0.25, "rtt": 1900}, "emulatorDetection": {"indicators": [], "riskPoints": 0, "isEmulator": false}, "botDetection": {"indicators": [], "riskPoints": 0, "isBot": false}, "spoofingDetection": {"indicators": [], "riskPoints": 0, "isSpoofed": false}, "behavioral": {"sessionDuration": 1567, "mouseEventsCount": 26, "keyboardEventsCount": 0, "scrollEventsCount": 12, "mousePattern": {"averageSpeed": 0.3688965421739147, "totalDistance": 105.13551451956569, "totalTime": 285, "eventsCount": 26}, "typingPattern": "insufficient_data"}}, "risk_score": 0, "risk_assessment": {"riskFactors": [{"type": "test_factor", "score": 10}], "recommendations": ["test_recommendation"]}, "user_id": "12345", "ip_address": "127.0.0.1", "timestamp": "2025-07-06 22:55:59", "created_at": 1751831759}, "f1dec4f": {"fingerprint": "f1dec4f", "components": {"canvas": "data:image/png;base64,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", "webgl": {"vendor": "WebKit", "renderer": "WebKit WebGL", "version": "WebGL 1.0 (OpenGL ES 2.0 Chromium)", "shadingLanguageVersion": "WebGL GLSL ES 1.0 (OpenGL ES GLSL ES 1.0 Chromium)", "unmaskedVendor": "Google Inc. (Google)", "unmaskedRenderer": "ANGLE (Google, Vulkan 1.3.0 (<PERSON><PERSON><PERSON><PERSON> (Subzero) (0x0000C0DE)), SwiftShader driver)", "extensions": "ANGLE_instanced_arrays,EXT_blend_minmax,EXT_clip_control,EXT_color_buffer_half_float,EXT_depth_clamp,EXT_float_blend,EXT_frag_depth,EXT_polygon_offset_clamp,EXT_shader_texture_lod,EXT_texture_compression_bptc,EXT_texture_compression_rgtc,EXT_texture_filter_anisotropic,EXT_texture_mirror_clamp_to_edge,EXT_sRGB,OES_element_index_uint,OES_fbo_render_mipmap,OES_standard_derivatives,OES_texture_float,OES_texture_float_linear,OES_texture_half_float,OES_texture_half_float_linear,OES_vertex_array_object,WEBGL_color_buffer_float,WEBGL_compressed_texture_astc,WEBGL_compressed_texture_etc,WEBGL_compressed_texture_etc1,WEBGL_compressed_texture_s3tc,WEBGL_compressed_texture_s3tc_srgb,WEBGL_debug_renderer_info,WEBGL_debug_shaders,WEBGL_depth_texture,WEBGL_draw_buffers,WEBGL_lose_context,WEBGL_multi_draw,WEBGL_polygon_mode", "maxTextureSize": 8192, "maxViewportDims": "8192,8192"}, "audio": "0,0.08264365047216415,0.16771934926509857,0.2492087185382843,0.3338141441345215,0.4166487157344818,0.5001381635665894,0.5834451913833618,0.6663544178009033,0.7517519593238831,0.8300729393959045,0.9228833317756653,0.9787084460258484,0.9228832721710205,0.8300728797912598,0.7517519593238831,0.6663542985916138,0.583445131778717,0.5001381635665894,0.41664862632751465,0.3338140547275543,0.2492087185382843,0.1677192896604538,0.08264358341693878,0,-0.08264380693435669,-0.16771942377090454,-0.2492087185382843,-0.3338142931461334,-0.4166487753391266,-0.5001381635665894,-0.5834453105926514,-0.6663544774055481,-0.7517519593238831,-0.8300730586051941,-0.9228834509849548,-0.9787084460258484,-0.9228830933570862,-0.8300728797912598,-0.7517519593238831,-0.6663541793823242,-0.583445131778717,-0.5001381635665894,-0.4166485071182251,-0.3338140547275543,-0.2492087185382843,-0.16771917045116425,-0.08264358341693878,1.1521713361162256e-7,0.0826437696814537,0.16771946847438812,0.24920883774757385,0.33381426334381104,0.41664883494377136,0.5001382827758789,0.5834453105926514,0.6663545370101929,0.7517520785331726,0.8300730586051941,0.9228835105895996,0.9787084460258484,0.9228830933570862,0.8300727605819702,0.7517518401145935,0.6663541793823242,0.5834450125694275,0.5001380443572998,0.4166485071182251,0.33381393551826477,0.24920859932899475,0.16771917045116425,0.08264345675706863,-2.3043426722324512e-7,-0.08264380693435669,-0.16771942377090454,-0.2492089569568634,-0.3338142931461334,-0.4166487753391266,-0.5001384019851685,-0.5834453105926514,-0.6663547158241272,-0.7517521977424622,-0.8300730586051941,-0.9228836894035339,-0.9787084460258484,-0.9228830933570862,-0.8300726413726807,-0.751751720905304,-0.6663541793823242,-0.5834448933601379,-0.5001379251480103,-0.4166485071182251,-0.3338138163089752,-0.2492084950208664,-0.16771917045116425,-0.08264333754777908,2.3043426722324512e-7,0.08264389634132385,0.16771958768367767,0.2492089569568634", "screen": {"width": 1366, "height": 768, "availWidth": 1366, "availHeight": 768, "colorDepth": 24, "pixelDepth": 24, "orientation": "landscape-primary", "devicePixelRatio": 1, "innerWidth": 1366, "innerHeight": 690, "outerWidth": 1366, "outerHeight": 768}, "system": {"platform": "Win32", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 YaBrowser/25.6.0.0 Safari/537.36", "language": "ru", "languages": "ru,en", "hardwareConcurrency": 4, "deviceMemory": "unknown", "maxTouchPoints": 0, "cookieEnabled": true, "doNotTrack": "1", "onLine": true}, "navigator": {"plugins": "PDF Viewer,Chrome PDF Viewer,Chromium PDF Viewer,Microsoft Edge PDF Viewer,WebKit built-in PDF", "mimeTypes": "application/pdf,text/pdf", "vendor": "Google Inc.", "product": "Gecko", "productSub": "20030107", "appName": "Netscape", "appVersion": "5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 YaBrowser/25.6.0.0 Safari/537.36"}, "timezone": {"timezone": "Europe/Moscow", "timezoneOffset": -180, "locale": "ru", "calendar": "gregory", "numberingSystem": "latn"}, "fonts": "Arial,Helvetica,Times New Roman,Courier New,Verdana,Georgia,Comic Sans MS,Trebuchet MS,Arial Black,Impact,Tahoma,Calibri", "hardware": {"hardwareConcurrency": 4, "deviceMemory": "unknown", "gpu": {"vendor": "WebKit", "renderer": "WebKit WebGL", "unmaskedVendor": "Google Inc. (Google)", "unmaskedRenderer": "ANGLE (Google, Vulkan 1.3.0 (<PERSON><PERSON><PERSON><PERSON> (Subzero) (0x0000C0DE)), SwiftShader driver)"}, "maxTouchPoints": 0, "vibration": true, "gamepad": true, "mediaDevices": false, "getUserMedia": false, "connection": {"effectiveType": "2g", "downlink": 0.25, "rtt": 1650, "saveData": false}, "storageQuota": "storage_quota_unavailable"}, "performance": {"jsPerformance": 1858, "canvasPerformance": 3, "webglPerformance": 3, "timing": {"navigationStart": 1751833285570, "loadEventEnd": 1751833285937, "domContentLoaded": 367, "loadComplete": 367}, "memory": {"usedJSHeapSize": 11247026, "totalJSHeapSize": 15686302, "jsHeapSizeLimit": 1953759232}}, "webrtc": {"localIPs": [], "candidatesCount": 0, "webrtcSupport": true}, "battery": "battery_unavailable", "sensors": {"accelerometer": false, "gyroscope": false, "magnetometer": false, "ambientLight": false, "proximity": false, "deviceOrientation": false, "deviceMotion": false}, "media": "media_unavailable", "storage": {"localStorage": true, "sessionStorage": true, "indexedDB": true, "webSQL": false, "cookies": true}, "network": {"onLine": true, "connection": {"effectiveType": "2g", "downlink": 0.25, "rtt": 1650, "saveData": false}, "effectiveType": "2g", "downlink": 0.25, "rtt": 1650}, "emulatorDetection": {"indicators": [], "riskPoints": 0, "isEmulator": false}, "botDetection": {"indicators": [], "riskPoints": 0, "isBot": false}, "spoofingDetection": {"indicators": [], "riskPoints": 0, "isSpoofed": false}, "behavioral": {"sessionDuration": 1346, "mouseEventsCount": 10, "keyboardEventsCount": 0, "scrollEventsCount": 7, "mousePattern": {"averageSpeed": 0.03433050104432676, "totalDistance": 42.6041517960095, "totalTime": 1241, "eventsCount": 10}, "typingPattern": "insufficient_data"}}, "risk_score": 0, "risk_assessment": {"totalRiskScore": 0, "riskFactors": [{"type": "device_fingerprint", "score": 0, "indicators": []}], "recommendations": ["normal_access"], "blockingReasons": []}, "user_id": "12345", "ip_address": "127.0.0.1", "timestamp": "2025-07-06 23:21:39", "created_at": 1751833299}, "47670783": {"fingerprint": "47670783", "components": {"canvas": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAARgAAAA8CAYAAAC9xKUYAAAAAXNSR0IArs4c6QAAFLpJREFUeF7tnQmUVNWZx3+3uoEWAUEJqICICyAMEw0IKgT0CBhIVMSRGR0dg4K2k4NryEwcGcfgcWJcc9SxEUSMiToqCDMGBWVcGZRAXFBjcMG2xXE5yGpsaLru1HerbnHr1au1q6urqt89pw90v7t83/fe93/f97/LU5R40ej9gCHAIGBw7N9DgO5A19jPQTE1tgA7Yz/bgP8D3nN/FOrbElc5EC+wQMVYQJWaJhrdGfg+cGrs51ggVCA5w8AbwHOxn9UK9ZcC9V023ehL0GUjbAEFVfdRcs97AdUrya7a3OAaXQ2MjIHJeOAEoGORrLUHWBMDm1XAWoVqLtLYbTZMADBtZvp2N3CbAYxGHwlMjf2MgjZ/u8hb/VVgMfC4Qn1SqU9DADCVemdLT6+iA4xGHwVcC5wPdCg9kxiJmoDfAjcr1J9LVMa8xQoAJm/TBQ1ztEDRAEajjwOui3AgUwrIqeSobs7VhbNZCvy7Qq3LuXWJNggApkRvTAWK1eoAo9EHA78GppWx/SR9WiSRl0J9XsZ6GNEDgCn3O1g+8rcawGi0zPzMikQrvwC6lY9J0kq6IxaF3aNQEt2UZQkApixvW1kK3SoAo9H9gIcjszNjytIqmYV+BThPoRoyVy29GgHAlN49qVSJCg4wGi0cy8JIWtSjUo0W02srcJFCCUdTViUAmLK6XWUtbMEARqO7ALdGVs9eWtYWyV34OuAqhWrMvWnbtAgApm3s3h5HLQjAaLQs218RWzDXHu0o62cmKZRsTyj5EgBMyd+iihGwxQAT41t+H1mgNqxirJKfIm8BExTqy/yaF69VADDFs3V7H6lFABNbNPc/gJC6QYH3gR8qlPxbsmXPDPTSj+G5zdCwC4b0gDP6w1jZQlrBJdiLVPybmzfAaLRELLJpsFfxxS7pESWCkUhGIppSLH81vHeHDUeMncL48bL1CxobG1m8eDE9G17i3jHQS/avV2AJAKb4NzUvgNHoIyKbE18GDi2+yGUx4mfAWIX6sMSk7amU+uDuu+8+oHPnzqxcuZJPPvmEgQMHMnXqVJ588kneeWohr8o8YAWWAGCKf1P3AcxF93eleu9ylL6DebVLEkS5tG4qYXU7VeHRuq5WVrUKuAjIJJXNfMNolnE7JzKVAeb6TpqYzNO8wr5FsOdyJA+bExmixa+OvTaXEVzH9zJax29st9FavuQcnuNxxjPSJ/DKRs6MQuyrIOAySqHkjJr8ysy661DqNPZWT6ZD0wS0usr8f+HFcuZNPuXOadOmXTFu3Dguv/xympv3bRzv2bMnixYt4uqrr+aX/TdyVvTWpS07dQ2T1SwadA9Wq1/Rh/Qc93n6YtPfw+r+TF23yvW8AMa9B/nbvVX0KYdOcwKYMxoOO23ZMz+Q3cZDs1VOnHoiy/kpfx0HCevIDexiNWfSh/3jAHMVw+LAJGNY0JjB4KxAJp1c6QDGynQafZPklD6XM4muue/NXAuMV6h8AWGfOgLyLQeYj5YtWzZAwKW+vj7JVFOmTGHYsGHUPzqXB0/JfIeXcBz/xumm4jS9juvU8rSNAoDJbNNKq5ETwGxc9LcbOzdVT5jE0/yAftzCm8Ye/ekSBwo3iphAXxO5uE5rDeh1aPu7F2Ck/o38kXfZaiIevyjDRjh+Ecx5rOIRopnKZPrxNlt9I5hU4OP3d5FnDtG9jwfQkZVMjkdEVoZ6dpnrEw/u8+nKz3/Uj9p7+9AcWk1IXx2PEN23o9pzDCF1vWmk1GS0fiTit++aCAbuBX5nrmnqCTf/hKqq/6A5fA73XyYgBk6USd1lm30eVFVdXR1+4YUXGDPGf4H1kCFDmD17Ngt/Pp2Xzsj8qAtgDDGHBsIKNZTl+i66OsuB1urDmaiuZDv70V9voZ/aSj/9NVeyinPUpTyu5zFSfWzab6Y7k7icBfo35m/S9yNKjgmKlnP12njkI9e60cgzDKVeRQ8zXEwdU3k9Xt9tL2NLhNX3vm3R5z1q97mxyttpDk+M29HeJ0X/yPqm7Wi9GqW6tTByzGzMCq2RNcAc8Hqn+e+sPedAsYOkQP3oYt7qUgRE+rG/AQDXyfuyPzN4iaeZZKIUb1nCJq5mjQGnbnQ0/aQDmHmMTRhL+hNnv5W3jJPLGG56JuDSwDcJcm7g6wRAsDJZuU+id0Lq5pVZxlvBp/GIxk8HC6gWDHsN2m/5klM+vCQjwFSFVqL1rcyvvdFxhOQUSS5KOqv1CqeubM14N/578gNbXVNT07RixQokRfIrRx99NNdffz2/vup81p6V/ol3AUFqegHDgstCHjSOL9HO2dQaoJjH70xqdZp+Jx71yPU7IqmrgNSl/D0N6sA4YNm2FkQEPJarYazUdxowulFPZgFj4mma/O4Cnv39lb2Du1HVdEU87ZSUxwVmUUReAugFxo4X3zsSuSewIQCY/BAwK4D50einZ29Y8/XNq8NnmvpejsV1uh3siV+XunewIWV64UYHx9DDF2BsirWQcQmpk1XX7cMFmFH0SpIzEwdjx9qOHHTnH51I9LaAsQkcjgDZEHqYHwuYHkBt+m3/98+6YOLz96SNYKpCjydEJek4GPdaxz3dCIeepjk8I/4mTn4eptfU1CzMBmB+ctH5PDsZjk8zP+h1Ym/6470u4rh1/NpLNOSXZgmYjdY/43b1uAEr71gCZhbg+qhtCXVdM6jQvX197TSzLgrOId61XGM8Cgw4mPyQJdYqI8BodO9Hqj/Y8PO9a78jkUZrA4xLBFvN/EheN/WxaYoLMBI9eQldiVL8AMLPghKZnM2z5tIYDjYg+Se2Gj7JApDbTmSU4kY37vVPQrsaBlzwMOEafWXKFCkXgIm+XRcQCk+iiT4mvWru8HepCOCQ4i9vPzBgv6MGDWLrVtlGlVyqqqro0mV/nl31B07/122yBGGCb0W/CQFvihZ1Wphfe168D3FWOcBd/haVPwqoHdicwvHdVAaUPtvYztu325cM5rWjFWBfRHJAkl5azzF/s6S6JXQLw321yEnLuXE2ALNoCZsutG/mXACmUCmSa2ALLK7TWyApJMDYMb0pX7pZKG/65Mot/Rx31OJdX5367YUFARjXycPmqwvizNHUyqdUV6G3//fhdD4w85rI1//4Dt/7x6/lrOKTfDuLOp2Q/clFHFXkyAQwVn5J8yRycAlsy5EI31QVHm0GcfmrlgFMYpToauAXrQQA0yJ8S1wHY0PF2IOq0ScDz6dKgew0dKrrfiSvRAZP8JHhOQQspLjkrR8H4zq7NwIpVIqUChxcMno6g5LSLtf6Lh/j5ZziHM/w3tc/sn68nJFjycYoxyIkby4RjG0PJ5q+wvqGNOkRUYDpT+cDD8v4wGQEGD/wiMrzMEr1M/p4uQ57PQqE0ajGOrTWcs7OGgNMftGRlyBPBzASDXnJdKuxH9HuWsOPKA9SpIzPS7oKiQATReuFwqrr+2tliujNzXwzSDgXO03sN1OTDoD8pqltFOLOPqWbRXIBxuV/rCzb2ONL8rpySR9CIudK8npBQ/pcwHtZz5pZELUEdc8unXY8ueu0Xqq2rqdxBEVD3gCTAwlZMIBJ56TetMd1dBv1yOyYBRg3ZbHpjxvZuIAjmWo2KZLMqnlBwQWOZjUd1AwTGclsm6tPU4dnE8jzHOzbIi+s4MbJK3mtUddzAOujmi9mQpxgzRVgpH26RXQWZNLNInmjBMuNCPci5K+kbwKAfhGGy9XM5rv8Jx/mtNDOpmLuGhi3z1T2sdPUbvs4iTxyD3yXetC3odS0rABGiNwoIHWPT6t6nTHNg1owgDE8iuOg7pj75GlweBaZhRHO4xWikcr2OMDE69Mv7vDSXzJX8mNgBlpH+00Xwdhp+2g0dW5MvMSp6MRr+7gdqexOU0uKBs+gGBrMIuWHgr5bBTRavqIoEUyn/LrNvtVzbKYbHXxX1mbfS9nVlIV3Q1t8Ip44aFXTo5nSI7FOwQCm7EwdCNyWFkgFMHLA9YVtKVg7GPtBhZI3c/5F3vQZZo9s5yFF0/sPHVZ9SN++aT/rqBS8+PKfmHTd1heALNbz5i9+0LLyLZAEMBp9tFkTAPLFxaC0ngX2xqKYjXkNEQ3zJyesQk3f0b9EvvckP9nslZbNmpcB/5WXbEGjwAIxC/gBjBwB2d6OvWyrB2Je5DMotW01eDBuYIHWtkACwGi0HDkkG3eyecu1tmztof9vI1+QPEqhJGIISmCBirOAF2D+Wb5iWHFalrZCcmD4naUtYiBdYIH8LOAFmDeQCdSgFNMCb0Y+fXJsMQcMxgosUCwLxAFGo+Uh37ffvVgSBOOIBY5TKAH3oAQWqCgLuAAjYfoVFaVd+Shzc+S7SpKeBiWwQEVZwAUYOflHDtkJSvEt8GeFGlz8YYMRAwu0rgUMwGj04ZGFdZu8Q63jMV7jIQZyMhO4Jq0kG3mRl6hjLLUMxP9Ao9ZVJffev+JDljGHY5nCCKYldWCv72YnHelCM02mnl/d3EdPajEgsvAuerxbAcrMtYxTilqtqZs/khfTdTlzLdOU4tTGMNf+ZhRJZwhfso5rNBw8fwSzWyLaP7zGQTUhbtKaVfNH8limvmRcNANduWau45YQxMFYa3ZrzV1WR2mjQDbpmhIO81A2Y2WSJbienwUswMiK0gfcLnaxhaVcSw3d2MZmzmQu3+HIlKNUIsAIwL7HKqZwE12IHs3YimW6QskK6qKXUgSYGEDOArZYgLlkHUei+RmKX903InYOqmOtmB7TLODkArJFN3o7GdACTNLWAAGM/+UBRnMRL1Ln+5Z/gtl8wXvGVAcxgF18ZSKYt3jK/O1vuCVuxme5je18bv4mfT/PXexlt7leTSdOYZaJfCRq+D1z6cXRbEK+yCoboromAJy3vRthuVGHtO3N4CQ5NiKr4GUH3qE0stNXN5HX1pO6xzCRj1gTryu6d6DGyCsRjpRRXBCPblwZRX4B5518GQcrr5ydhnd5e+b6R83XMWMRQ3c0R4YUXeUtTIjjlTbH9h+mFIe6b27reFrxZQiO1ZrPmjVPVSnOkwhG+lQwXYdokOvyu9QRx+2kODUU4gL5W1izUynmuM7rRgS2jUQ5nr+njCKsnLs1b0v0IrKb8eGF+0Zwm5+fSaTTKYQ5n1hBJwswAhgozt4d5gZvpGWjIxQbU/XbTny6pNS0AJPEv7iAIM7kBQz5WyM7jMNIkWjnG7YYoNjBF7zB0jgo2GhoMKfSjd4GXIbHUg17TfqQvr5lm0lbauia0LdEUgJO3rTG/b0/wxNSHtt3bwaaFE90qmd9XC4LkC4wuHfHjWCsXDadkrZb2BQHRrdv6UN0EHlkXAs2+3NQgo62L5Hzie7X7Fzz3IM3iHPEUoMT3dDfpgY25Hff1irEp1ozF8Vmm8a4b2/jqIpZWrFJrgsgSX2lWC/j5RrBeFMXaY9iioCTDjPc/l+Ayr3W2My2bFIk078tTopk5ITJKhQNJ12QDaKVksKVuDBKow+ObA2IHg0fK14n9qY/ftyFW6cHfRMc3UZDP2SOb5olzvkFG32dT0Ryr69hUbyuN21xQdHqYsc+mVm8wn2RI72jYCMlEweTCWBc0HX1F4D1plYuIIsONppz5bxnyRmPNR6241qDtT7cg9S1AOK+sQXfDcBollq+wQdgEvgYASzbXy4AY6KlMHM0POByO9Kfgs91mAYXYNznKhsOxk2DvHYwkZ1muI2yXJCNRTvTNSxXynBKnbz8TGm6YGVLJQBjTq1L9eYWJ/ZGAn58i01tTmK6SXXcqMfP8b0pgqQrbgTjEq8uwDzDL5OiKSu7m7K5+kiK8n1m8jLzE9IhN7LyI27zBZh61iUBiFcHm1q6cjbctGHV1on1sg/MF2DEgd3w34KEgrpiAYzlRsSBva4haU9jM4vcVMglWbMBGKNTmD8IUPqRvN4xrQ0I85REaS5n4+VkKtuVS1M7ARjZbCff3THFOt12krfHWC5kK58mzRh5AUacU9KkifyUl5iHpEfixC6wWO4klwgmE8C4UYVrcr9opS0BJoWchuhNNXtSQgCT3exUbMbHy8OkmkUy6VSI422Ulg3A2BkuAzAwnRBzLYcU8DJtDzoCMAkL7FLNBlkeQbgTL9chaqRKow5lKF/yPjY98puZyQVgMqVINtXypk/eKKytU6QUcpoFd6kAplRSJG+0lO4xdp3cRjcpAcYzBW37FfJ5TzO3dariEi+Ja1MzQwN6UrcAYEoDYJZGtghEv0fi4Tu8TupNe6yTSDuX5LXrYGzKYtMf6c9GNnba2661yTZF8pKtLnBIWuWSq159hBdxyeeWkrxuFOLHQWUiee11K+cHQ1d//OrCJ0fUVPFjPw5GaQY4U7BxYlXaFytFkrG8XIgljYUDMg9RjPCNkbzx9Th2JinfdTB+HIw7lh/5LHyMS5a3vcu1LwkkgpG54FHuG9198F1zCBis57H4zInLeQzgBD7j7YSFdqkW6rntBFiO4EQ28JTp10sQ+4Ged5ranYr2XnPBzfZlp5/7cqxJ2VIttMuXgxGA9ZumbqIxPmXuJ+fpHW5Y373pkBEpUyRNNw1dZeraO03dEoCJzypBRz9ntLyLhj1xgtUTbbhci3cxnHvNTm+H4b1MC/f87JBuetyCn11oF5C8bQ9mAjCykEXO4A1KK1rAj+j2GS7llgF31qcVxQy6DixQUAsIwHxu1qMFpWAW8KaBmabDnYHrIySvbNtIKgHAFOz2BB0V0QICMLI6NPlTmkUUohKH8k6Zp1rM59H9C4WSdUkBwFTiQ9EOdRKAaSzG50naoW3zUXm3QtXk0zBoE1igFC0QAExp3ZUAYErrfgTStNACAQfTQgMWuHnKFKnA4wTdBRYoigUEYIKDpopi6qwGCQ6eyspMQaVysUAwTV1adyo4ALy07kcgTQstkLDQroV9Bc1bboHXFOqElncT9BBYoDQsIACTsFWgNMRqt1Isi3xdYEq71T5QvOIsIAAj5x/8U8VpVp4KBV8XKM/7FkidwgICMEnn8QbWajMLtNm5vG2mcTBwRVtAAEZy/jUVrWX5KHeiQkUPIg5KYIEKsIAATPfIB9e2VoAulaBCD4WSrRtBCSxQERb4fw8TNADlJYX9AAAAAElFTkSuQmCC", "webgl": {"vendor": "WebKit", "renderer": "WebKit WebGL", "version": "WebGL 1.0 (OpenGL ES 2.0 Chromium)", "shadingLanguageVersion": "WebGL GLSL ES 1.0 (OpenGL ES GLSL ES 1.0 Chromium)", "unmaskedVendor": "Google Inc. (Google)", "unmaskedRenderer": "ANGLE (Google, Vulkan 1.3.0 (<PERSON><PERSON><PERSON><PERSON> (Subzero) (0x0000C0DE)), SwiftShader driver)", "extensions": "ANGLE_instanced_arrays,EXT_blend_minmax,EXT_clip_control,EXT_color_buffer_half_float,EXT_depth_clamp,EXT_float_blend,EXT_frag_depth,EXT_polygon_offset_clamp,EXT_shader_texture_lod,EXT_texture_compression_bptc,EXT_texture_compression_rgtc,EXT_texture_filter_anisotropic,EXT_texture_mirror_clamp_to_edge,EXT_sRGB,OES_element_index_uint,OES_fbo_render_mipmap,OES_standard_derivatives,OES_texture_float,OES_texture_float_linear,OES_texture_half_float,OES_texture_half_float_linear,OES_vertex_array_object,WEBGL_color_buffer_float,WEBGL_compressed_texture_astc,WEBGL_compressed_texture_etc,WEBGL_compressed_texture_etc1,WEBGL_compressed_texture_s3tc,WEBGL_compressed_texture_s3tc_srgb,WEBGL_debug_renderer_info,WEBGL_debug_shaders,WEBGL_depth_texture,WEBGL_draw_buffers,WEBGL_lose_context,WEBGL_multi_draw,WEBGL_polygon_mode", "maxTextureSize": 8192, "maxViewportDims": "8192,8192"}, "audio": "0,0.08264365047216415,0.16771934926509857,0.2492087185382843,0.3338141441345215,0.4166487157344818,0.5001381635665894,0.5834451913833618,0.6663544178009033,0.7517519593238831,0.8300729393959045,0.9228833317756653,0.9787084460258484,0.9228832721710205,0.8300728797912598,0.7517519593238831,0.6663542985916138,0.583445131778717,0.5001381635665894,0.41664862632751465,0.3338140547275543,0.2492087185382843,0.1677192896604538,0.08264358341693878,0,-0.08264380693435669,-0.16771942377090454,-0.2492087185382843,-0.3338142931461334,-0.4166487753391266,-0.5001381635665894,-0.5834453105926514,-0.6663544774055481,-0.7517519593238831,-0.8300730586051941,-0.9228834509849548,-0.9787084460258484,-0.9228830933570862,-0.8300728797912598,-0.7517519593238831,-0.6663541793823242,-0.583445131778717,-0.5001381635665894,-0.4166485071182251,-0.3338140547275543,-0.2492087185382843,-0.16771917045116425,-0.08264358341693878,1.1521713361162256e-7,0.0826437696814537,0.16771946847438812,0.24920883774757385,0.33381426334381104,0.41664883494377136,0.5001382827758789,0.5834453105926514,0.6663545370101929,0.7517520785331726,0.8300730586051941,0.9228835105895996,0.9787084460258484,0.9228830933570862,0.8300727605819702,0.7517518401145935,0.6663541793823242,0.5834450125694275,0.5001380443572998,0.4166485071182251,0.33381393551826477,0.24920859932899475,0.16771917045116425,0.08264345675706863,-2.3043426722324512e-7,-0.08264380693435669,-0.16771942377090454,-0.2492089569568634,-0.3338142931461334,-0.4166487753391266,-0.5001384019851685,-0.5834453105926514,-0.6663547158241272,-0.7517521977424622,-0.8300730586051941,-0.9228836894035339,-0.9787084460258484,-0.9228830933570862,-0.8300726413726807,-0.751751720905304,-0.6663541793823242,-0.5834448933601379,-0.5001379251480103,-0.4166485071182251,-0.3338138163089752,-0.2492084950208664,-0.16771917045116425,-0.08264333754777908,2.3043426722324512e-7,0.08264389634132385,0.16771958768367767,0.2492089569568634", "screen": {"width": 1366, "height": 768, "availWidth": 1366, "availHeight": 768, "colorDepth": 24, "pixelDepth": 24, "orientation": "landscape-primary", "devicePixelRatio": 1, "innerWidth": 1366, "innerHeight": 690, "outerWidth": 1366, "outerHeight": 768}, "system": {"platform": "Win32", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 YaBrowser/25.6.0.0 Safari/537.36", "language": "ru", "languages": "ru,en", "hardwareConcurrency": 4, "deviceMemory": "unknown", "maxTouchPoints": 0, "cookieEnabled": true, "doNotTrack": "1", "onLine": true}, "navigator": {"plugins": "PDF Viewer,Chrome PDF Viewer,Chromium PDF Viewer,Microsoft Edge PDF Viewer,WebKit built-in PDF", "mimeTypes": "application/pdf,text/pdf", "vendor": "Google Inc.", "product": "Gecko", "productSub": "20030107", "appName": "Netscape", "appVersion": "5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 YaBrowser/25.6.0.0 Safari/537.36"}, "timezone": {"timezone": "Europe/Moscow", "timezoneOffset": -180, "locale": "ru", "calendar": "gregory", "numberingSystem": "latn"}, "fonts": "Arial,Helvetica,Times New Roman,Courier New,Verdana,Georgia,Comic Sans MS,Trebuchet MS,Arial Black,Impact,Tahoma,Calibri", "hardware": {"hardwareConcurrency": 4, "deviceMemory": "unknown", "gpu": {"vendor": "WebKit", "renderer": "WebKit WebGL", "unmaskedVendor": "Google Inc. (Google)", "unmaskedRenderer": "ANGLE (Google, Vulkan 1.3.0 (<PERSON><PERSON><PERSON><PERSON> (Subzero) (0x0000C0DE)), SwiftShader driver)"}, "maxTouchPoints": 0, "vibration": true, "gamepad": true, "mediaDevices": false, "getUserMedia": false, "connection": {"effectiveType": "2g", "downlink": 0.25, "rtt": 1650, "saveData": false}, "storageQuota": "storage_quota_unavailable"}, "performance": {"jsPerformance": 1846, "canvasPerformance": 2, "webglPerformance": 1, "timing": {"navigationStart": 1751833285570, "loadEventEnd": 1751833285937, "domContentLoaded": 367, "loadComplete": 367}, "memory": {"usedJSHeapSize": 10216032, "totalJSHeapSize": 15573116, "jsHeapSizeLimit": 1953759232}}, "webrtc": {"localIPs": [], "candidatesCount": 0, "webrtcSupport": true}, "battery": "battery_unavailable", "sensors": {"accelerometer": false, "gyroscope": false, "magnetometer": false, "ambientLight": false, "proximity": false, "deviceOrientation": false, "deviceMotion": false}, "media": "media_unavailable", "storage": {"localStorage": true, "sessionStorage": true, "indexedDB": true, "webSQL": false, "cookies": true}, "network": {"onLine": true, "connection": {"effectiveType": "2g", "downlink": 0.25, "rtt": 1650, "saveData": false}, "effectiveType": "2g", "downlink": 0.25, "rtt": 1650}, "emulatorDetection": {"indicators": [], "riskPoints": 0, "isEmulator": false}, "botDetection": {"indicators": [], "riskPoints": 0, "isBot": false}, "spoofingDetection": {"indicators": [], "riskPoints": 0, "isSpoofed": false}, "behavioral": {"sessionDuration": 1734, "mouseEventsCount": 2, "keyboardEventsCount": 0, "scrollEventsCount": 7, "mousePattern": {"averageSpeed": 0.0023752969121140144, "totalDistance": 1, "totalTime": 421, "eventsCount": 2}, "typingPattern": "insufficient_data"}}, "risk_score": 0, "risk_assessment": {"riskFactors": [{"type": "test_factor", "score": 10}], "recommendations": ["test_recommendation"]}, "user_id": "12345", "ip_address": "127.0.0.1", "timestamp": "2025-07-06 23:21:39", "created_at": 1751833299}}