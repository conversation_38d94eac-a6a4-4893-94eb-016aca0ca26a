<?php
/**
 * Простой тест для создания отпечатков устройств
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    // Получаем данные запроса
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Нет входных данных');
    }
    
    error_log("DEBUG: test-fingerprint-simple получил данные: " . json_encode($input));
    
    $action = $input['action'] ?? 'unknown';
    
    if ($action === 'register_device') {
        // Простая регистрация отпечатка
        $fingerprintData = $input['fingerprint_data'] ?? null;
        $initData = $input['initData'] ?? null;
        
        if (!$fingerprintData || !$initData) {
            throw new Exception('Отсутствуют fingerprint_data или initData');
        }
        
        error_log("DEBUG: Регистрируем отпечаток: " . $fingerprintData['fingerprint']);
        
        // Определяем user_id
        $userId = 12345; // Тестовый пользователь
        if (strpos($initData, 'test_fallback_mode') === 0) {
            $userId = 12345;
        } else {
            // Попробуем извлечь из реальных данных
            parse_str($initData, $initDataParts);
            if (isset($initDataParts['user'])) {
                $userArray = json_decode($initDataParts['user'], true);
                if ($userArray && isset($userArray['id'])) {
                    $userId = $userArray['id'];
                }
            }
        }
        
        // Создаем структуру отпечатка
        $fingerprint = $fingerprintData['fingerprint'];
        $components = $fingerprintData['components'] ?? [];
        
        // Определяем тип устройства
        $deviceType = 'unknown';
        if (isset($components['system']['userAgent'])) {
            $userAgent = $components['system']['userAgent'];
            if (strpos($userAgent, 'Mobile') !== false || strpos($userAgent, 'Android') !== false) {
                $deviceType = 'mobile';
            } elseif (strpos($userAgent, 'iPad') !== false) {
                $deviceType = 'tablet';
            } else {
                $deviceType = 'desktop';
            }
        }
        
        // Создаем запись для сохранения
        $fingerprintRecord = [
            'fingerprint' => $fingerprint,
            'user_id' => $userId,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'timestamp' => date('Y-m-d H:i:s'),
            'created_at' => time(),
            'device_type' => $deviceType,
            'risk_score' => 0,
            'components' => $components
        ];
        
        // Загружаем существующие отпечатки
        $fingerprintsFile = __DIR__ . '/../database/advanced_fingerprints.json';
        $fingerprints = [];
        
        if (file_exists($fingerprintsFile)) {
            $content = file_get_contents($fingerprintsFile);
            $fingerprints = json_decode($content, true) ?: [];
        }
        
        // Добавляем новый отпечаток
        $fingerprints[$fingerprint] = $fingerprintRecord;
        
        // Ограничиваем до 5000 записей
        if (count($fingerprints) > 5000) {
            $sorted = $fingerprints;
            uasort($sorted, function($a, $b) {
                return $b['created_at'] - $a['created_at'];
            });
            $fingerprints = array_slice($sorted, 0, 5000, true);
        }
        
        // Сохраняем в файл
        $saved = file_put_contents($fingerprintsFile, json_encode($fingerprints, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        
        if ($saved === false) {
            throw new Exception('Не удалось сохранить отпечаток в файл');
        }
        
        error_log("DEBUG: Отпечаток сохранен успешно. User ID: $userId, Total fingerprints: " . count($fingerprints));
        
        echo json_encode([
            'success' => true,
            'fingerprint' => $fingerprint,
            'user_id' => $userId,
            'device_type' => $deviceType,
            'total_fingerprints' => count($fingerprints),
            'message' => 'Отпечаток успешно зарегистрирован'
        ]);
        
    } else {
        throw new Exception('Неизвестное действие: ' . $action);
    }
    
} catch (Exception $e) {
    error_log("DEBUG: test-fingerprint-simple ошибка: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'debug' => [
            'file' => __FILE__,
            'line' => $e->getLine()
        ]
    ]);
}
?>
