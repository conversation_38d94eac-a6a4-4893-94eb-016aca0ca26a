/**
 * test_phase4_advanced_fingerprint.js
 * Тестирование Фазы 4 - Расширенное устройство fingerprinting
 * 
 * Проверяет:
 * - AdvancedDeviceFingerprint
 * - AdvancedFraudManager
 * - Интеграцию с серверной частью
 * - Детекцию эмуляторов и ботов
 * - Анализ рисков
 */

console.log('🧪 [Phase4Test] Запуск тестирования Фазы 4 - Расширенное fingerprinting');

// Мокаем Telegram WebApp для тестирования
if (typeof window !== 'undefined') {
    window.Telegram = {
        WebApp: {
            initData: 'user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&auth_date=1234567890&hash=test_hash',
            ready: () => console.log('Telegram WebApp ready'),
            expand: () => console.log('Telegram WebApp expanded')
        }
    };
}

class Phase4Tester {
    constructor() {
        this.testResults = {
            advancedFingerprint: false,
            advancedFraudManager: false,
            serverIntegration: false,
            emulatorDetection: false,
            botDetection: false,
            riskAnalysis: false,
            totalTests: 6,
            passedTests: 0
        };
        
        this.testData = {
            fingerprint: null,
            riskScore: 0,
            components: null
        };
    }
    
    async runAllTests() {
        console.log('🚀 [Phase4Test] Начинаем комплексное тестирование Фазы 4...\n');
        
        try {
            // Тест 1: AdvancedDeviceFingerprint
            await this.testAdvancedDeviceFingerprint();
            
            // Тест 2: AdvancedFraudManager
            await this.testAdvancedFraudManager();
            
            // Тест 3: Серверная интеграция (сначала синхронизируем данные)
            await this.testServerIntegration();

            // Тест 4: Детекция эмуляторов
            await this.testEmulatorDetection();

            // Тест 5: Детекция ботов
            await this.testBotDetection();

            // Тест 6: Анализ рисков (после синхронизации данных)
            await this.testRiskAnalysis();
            
            // Итоговый отчет
            this.generateFinalReport();
            
        } catch (error) {
            console.error('❌ [Phase4Test] Критическая ошибка тестирования:', error);
        }
    }
    
    async testAdvancedDeviceFingerprint() {
        console.log('📊 [Test 1] Тестирование AdvancedDeviceFingerprint...');
        
        try {
            // Проверяем наличие класса
            if (typeof window.AdvancedDeviceFingerprint === 'undefined') {
                throw new Error('AdvancedDeviceFingerprint класс не найден');
            }
            
            // Создаем экземпляр
            const advancedFingerprint = new window.AdvancedDeviceFingerprint();
            
            // Генерируем расширенный отпечаток
            console.log('   🔍 Генерация расширенного отпечатка...');
            const result = await advancedFingerprint.generateAdvancedFingerprint();
            
            // Проверяем результат
            if (!result.fingerprint || !result.components || typeof result.riskScore !== 'number') {
                throw new Error('Неполные данные fingerprint');
            }
            
            // Сохраняем данные для других тестов
            this.testData.fingerprint = result.fingerprint;
            this.testData.riskScore = result.riskScore;
            this.testData.components = result.components;
            
            console.log('   ✅ Fingerprint сгенерирован:', result.fingerprint.substring(0, 16) + '...');
            console.log('   📊 Risk Score:', result.riskScore);
            console.log('   🔍 Компоненты:', Object.keys(result.components).length);
            
            // Проверяем наличие новых компонентов
            const expectedComponents = [
                'hardware', 'performance', 'webrtc', 'battery', 'sensors',
                'media', 'storage', 'network', 'emulatorDetection',
                'botDetection', 'spoofingDetection', 'behavioral'
            ];
            
            const missingComponents = expectedComponents.filter(comp => 
                !result.components.hasOwnProperty(comp)
            );
            
            if (missingComponents.length > 0) {
                console.warn('   ⚠️ Отсутствующие компоненты:', missingComponents);
            }
            
            this.testResults.advancedFingerprint = true;
            this.testResults.passedTests++;
            console.log('   ✅ [Test 1] AdvancedDeviceFingerprint - PASSED\n');
            
        } catch (error) {
            console.error('   ❌ [Test 1] AdvancedDeviceFingerprint - FAILED:', error.message);
            console.log('');
        }
    }
    
    async testAdvancedFraudManager() {
        console.log('🛡️ [Test 2] Тестирование AdvancedFraudManager...');
        
        try {
            // Проверяем наличие класса
            if (typeof window.AdvancedFraudManager === 'undefined') {
                throw new Error('AdvancedFraudManager класс не найден');
            }
            
            // Создаем экземпляр
            const advancedFraudManager = new window.AdvancedFraudManager();
            
            // Инициализируем
            console.log('   🚀 Инициализация AdvancedFraudManager...');
            const initData = window.Telegram?.WebApp?.initData || 'test_init_data';
            const result = await advancedFraudManager.initialize(initData);
            
            // Проверяем результат
            if (!result.success) {
                throw new Error('Инициализация не удалась: ' + (result.error || 'unknown'));
            }
            
            console.log('   ✅ Инициализация успешна');
            console.log('   📊 Risk Score:', result.riskScore);
            console.log('   🔍 High Risk:', result.isHighRisk);
            console.log('   🚫 Should Block:', result.shouldBlock);
            
            // Проверяем статус системы
            const status = advancedFraudManager.getSystemStatus();
            console.log('   📋 Статус компонентов:', status.components);
            
            this.testResults.advancedFraudManager = true;
            this.testResults.passedTests++;
            console.log('   ✅ [Test 2] AdvancedFraudManager - PASSED\n');
            
        } catch (error) {
            console.error('   ❌ [Test 2] AdvancedFraudManager - FAILED:', error.message);
            console.log('');
        }
    }
    
    async testServerIntegration() {
        console.log('🌐 [Test 3] Тестирование серверной интеграции...');

        try {
            if (!this.testData.fingerprint) {
                throw new Error('Нет данных fingerprint для тестирования');
            }

            // Тестируем синхронизацию с сервером
            console.log('   📤 Отправка данных на сервер...');

            const syncData = {
                action: 'sync_advanced_fingerprint',
                fingerprint: this.testData.fingerprint,
                components: this.testData.components,
                riskScore: this.testData.riskScore,
                riskAssessment: {
                    riskFactors: [{ type: 'test_factor', score: 10 }],
                    recommendations: ['test_recommendation']
                },
                initData: window.Telegram?.WebApp?.initData || 'test_init_data'
            };

            console.log('   📋 Отправляемые данные:', JSON.stringify(syncData, null, 2));

            const response = await fetch('/api/fraud-detection.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(syncData)
            });

            console.log('   📋 HTTP статус:', response.status);

            if (!response.ok) {
                const errorText = await response.text();
                console.error('   📋 Ответ сервера:', errorText);
                throw new Error('Ошибка HTTP: ' + response.status);
            }

            const result = await response.json();

            if (!result.success) {
                throw new Error('Серверная ошибка: ' + (result.error || 'unknown'));
            }

            console.log('   ✅ Синхронизация успешна');
            console.log('   📊 Дополнительный анализ:', result.additional_analysis ? 'Выполнен' : 'Не выполнен');

            // Небольшая задержка для обеспечения сохранения данных
            await new Promise(resolve => setTimeout(resolve, 500));

            // Тестируем получение профиля устройства
            console.log('   📋 Получение профиля устройства...');

            const profileResponse = await fetch('/api/fraud-detection.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    action: 'get_device_risk_profile',
                    fingerprint: this.testData.fingerprint
                })
            });

            if (profileResponse.ok) {
                const profileResult = await profileResponse.json();
                if (profileResult.success) {
                    console.log('   ✅ Профиль устройства получен');
                    console.log('   📊 Репутация:', profileResult.profile.reputation);
                } else {
                    console.warn('   ⚠️ Ошибка получения профиля:', profileResult.error);
                }
            } else {
                console.warn('   ⚠️ Ошибка HTTP при получении профиля:', profileResponse.status);
            }

            this.testResults.serverIntegration = true;
            this.testResults.passedTests++;
            console.log('   ✅ [Test 3] Серверная интеграция - PASSED\n');

        } catch (error) {
            console.error('   ❌ [Test 3] Серверная интеграция - FAILED:', error.message);
            console.log('');
        }
    }
    
    async testEmulatorDetection() {
        console.log('🤖 [Test 4] Тестирование детекции эмуляторов...');
        
        try {
            if (!this.testData.components || !this.testData.components.emulatorDetection) {
                throw new Error('Данные детекции эмуляторов не найдены');
            }
            
            const emulatorData = this.testData.components.emulatorDetection;
            
            console.log('   🔍 Анализ индикаторов эмуляторов...');
            console.log('   📊 Количество индикаторов:', emulatorData.indicators?.length || 0);
            console.log('   📊 Risk Points:', emulatorData.riskPoints || 0);
            console.log('   🤖 Is Emulator:', emulatorData.isEmulator || false);
            
            if (emulatorData.indicators) {
                console.log('   📋 Индикаторы:', emulatorData.indicators);
            }
            
            // Проверяем логику детекции
            const hasValidStructure = 
                typeof emulatorData.riskPoints === 'number' &&
                typeof emulatorData.isEmulator === 'boolean' &&
                Array.isArray(emulatorData.indicators);
            
            if (!hasValidStructure) {
                throw new Error('Неверная структура данных детекции эмуляторов');
            }
            
            this.testResults.emulatorDetection = true;
            this.testResults.passedTests++;
            console.log('   ✅ [Test 4] Детекция эмуляторов - PASSED\n');
            
        } catch (error) {
            console.error('   ❌ [Test 4] Детекция эмуляторов - FAILED:', error.message);
            console.log('');
        }
    }
    
    async testBotDetection() {
        console.log('🕷️ [Test 5] Тестирование детекции ботов...');
        
        try {
            if (!this.testData.components || !this.testData.components.botDetection) {
                throw new Error('Данные детекции ботов не найдены');
            }
            
            const botData = this.testData.components.botDetection;
            
            console.log('   🔍 Анализ индикаторов ботов...');
            console.log('   📊 Количество индикаторов:', botData.indicators?.length || 0);
            console.log('   📊 Risk Points:', botData.riskPoints || 0);
            console.log('   🕷️ Is Bot:', botData.isBot || false);
            
            if (botData.indicators) {
                console.log('   📋 Индикаторы:', botData.indicators);
            }
            
            // Проверяем логику детекции
            const hasValidStructure = 
                typeof botData.riskPoints === 'number' &&
                typeof botData.isBot === 'boolean' &&
                Array.isArray(botData.indicators);
            
            if (!hasValidStructure) {
                throw new Error('Неверная структура данных детекции ботов');
            }
            
            this.testResults.botDetection = true;
            this.testResults.passedTests++;
            console.log('   ✅ [Test 5] Детекция ботов - PASSED\n');
            
        } catch (error) {
            console.error('   ❌ [Test 5] Детекция ботов - FAILED:', error.message);
            console.log('');
        }
    }
    
    async testRiskAnalysis() {
        console.log('📊 [Test 6] Тестирование анализа рисков...');

        try {
            if (!this.testData.fingerprint) {
                throw new Error('Нет данных fingerprint для анализа рисков');
            }

            // Дополнительная задержка для обеспечения сохранения данных
            console.log('   ⏳ Ожидание сохранения данных...');
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Тестируем расширенный анализ рисков
            console.log('   🔍 Выполнение расширенного анализа рисков...');

            const analysisData = {
                action: 'analyze_advanced_risks',
                fingerprint: this.testData.fingerprint
            };

            console.log('   📋 Отправляемые данные для анализа:', JSON.stringify(analysisData, null, 2));

            const analysisResponse = await fetch('/api/fraud-detection.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(analysisData)
            });

            if (!analysisResponse.ok) {
                const errorText = await analysisResponse.text();
                console.error('   📋 Ответ сервера:', errorText);
                throw new Error('Ошибка HTTP при анализе рисков: ' + analysisResponse.status);
            }

            const analysisResult = await analysisResponse.json();

            if (!analysisResult.success) {
                throw new Error('Ошибка анализа рисков: ' + (analysisResult.error || 'unknown'));
            }

            const analysis = analysisResult.analysis;

            console.log('   ✅ Анализ рисков выполнен');
            console.log('   📊 Базовый Risk Score:', analysis.base_risk_score);
            console.log('   📊 Итоговый Risk Score:', analysis.total_risk_score);
            console.log('   📊 Репутация устройства:', analysis.device_reputation);
            console.log('   📊 Количество факторов риска:', analysis.risk_factors?.length || 0);
            console.log('   📋 Рекомендации:', analysis.recommendations?.length || 0);

            if (analysis.risk_factors && analysis.risk_factors.length > 0) {
                console.log('   ⚠️ Факторы риска:');
                analysis.risk_factors.forEach(factor => {
                    console.log(`      - ${factor.type}: ${factor.severity} (${factor.score} points)`);
                });
            }

            this.testResults.riskAnalysis = true;
            this.testResults.passedTests++;
            console.log('   ✅ [Test 6] Анализ рисков - PASSED\n');

        } catch (error) {
            console.error('   ❌ [Test 6] Анализ рисков - FAILED:', error.message);
            console.log('');
        }
    }
    
    generateFinalReport() {
        console.log('📋 [Phase4Test] ИТОГОВЫЙ ОТЧЕТ ФАЗЫ 4');
        console.log('=' .repeat(50));
        
        const successRate = (this.testResults.passedTests / this.testResults.totalTests * 100).toFixed(1);
        
        console.log(`📊 Общий результат: ${this.testResults.passedTests}/${this.testResults.totalTests} тестов пройдено (${successRate}%)`);
        console.log('');
        
        console.log('📋 Детализация тестов:');
        console.log(`   1. AdvancedDeviceFingerprint: ${this.testResults.advancedFingerprint ? '✅ PASSED' : '❌ FAILED'}`);
        console.log(`   2. AdvancedFraudManager: ${this.testResults.advancedFraudManager ? '✅ PASSED' : '❌ FAILED'}`);
        console.log(`   3. Серверная интеграция: ${this.testResults.serverIntegration ? '✅ PASSED' : '❌ FAILED'}`);
        console.log(`   4. Детекция эмуляторов: ${this.testResults.emulatorDetection ? '✅ PASSED' : '❌ FAILED'}`);
        console.log(`   5. Детекция ботов: ${this.testResults.botDetection ? '✅ PASSED' : '❌ FAILED'}`);
        console.log(`   6. Анализ рисков: ${this.testResults.riskAnalysis ? '✅ PASSED' : '❌ FAILED'}`);
        console.log('');
        
        if (this.testData.fingerprint) {
            console.log('📊 Данные тестирования:');
            console.log(`   Fingerprint: ${this.testData.fingerprint.substring(0, 32)}...`);
            console.log(`   Risk Score: ${this.testData.riskScore}`);
            console.log(`   Компоненты: ${this.testData.components ? Object.keys(this.testData.components).length : 0}`);
        }
        
        console.log('');
        
        if (successRate >= 100) {
            console.log('🎉 ФАЗА 4 ЗАВЕРШЕНА УСПЕШНО! Все системы работают корректно.');
        } else if (successRate >= 80) {
            console.log('✅ ФАЗА 4 В ОСНОВНОМ ГОТОВА. Есть незначительные проблемы для исправления.');
        } else if (successRate >= 60) {
            console.log('⚠️ ФАЗА 4 ЧАСТИЧНО ГОТОВА. Требуются исправления критичных компонентов.');
        } else {
            console.log('❌ ФАЗА 4 НЕ ГОТОВА. Требуется серьезная доработка системы.');
        }
        
        console.log('=' .repeat(50));
    }
}

// Запускаем тестирование
const phase4Tester = new Phase4Tester();
phase4Tester.runAllTests();
