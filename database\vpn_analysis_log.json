[{"timestamp": 1751827177, "ip": "127.0.0.1", "risk_score": 0, "vpn_detected": false, "analysis": {"ip_analysis": {"ip": "127.0.0.1", "is_private": true, "is_reserved": true, "reverse_dns": "argun.loc", "asn_info": null}, "header_analysis": {"user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* YaBrowser/******** Safari/537.36", "accept_language": "ru,en;q=0.9", "x_forwarded_for": null, "x_real_ip": null, "via": null, "proxy_headers": []}, "geolocation_analysis": {"server_ip_geo": {"country": "Unknown", "city": "Unknown", "latitude": null, "longitude": null}, "client_timezone": null, "client_geo": null, "mismatch_detected": false}, "reputation_analysis": {"checked": false, "is_malicious": false, "is_vpn": false, "confidence": 0}, "client_analysis": []}, "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* YaBrowser/******** Safari/537.36"}, {"timestamp": 1751827431, "ip": "127.0.0.1", "risk_score": 0, "vpn_detected": false, "analysis": {"ip_analysis": {"ip": "127.0.0.1", "is_private": true, "is_reserved": true, "reverse_dns": "argun.loc", "asn_info": null}, "header_analysis": {"user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* YaBrowser/******** Safari/537.36", "accept_language": "ru,en;q=0.9", "x_forwarded_for": null, "x_real_ip": null, "via": null, "proxy_headers": []}, "geolocation_analysis": {"server_ip_geo": {"country": "Unknown", "city": "Unknown", "latitude": null, "longitude": null}, "client_timezone": null, "client_geo": null, "mismatch_detected": false}, "reputation_analysis": {"checked": false, "is_malicious": false, "is_vpn": false, "confidence": 0}, "client_analysis": []}, "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* YaBrowser/******** Safari/537.36"}, {"timestamp": 1751827443, "ip": "127.0.0.1", "risk_score": 0, "vpn_detected": false, "analysis": {"ip_analysis": {"ip": "127.0.0.1", "is_private": true, "is_reserved": true, "reverse_dns": "argun.loc", "asn_info": null}, "header_analysis": {"user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* YaBrowser/******** Safari/537.36", "accept_language": "ru,en;q=0.9", "x_forwarded_for": null, "x_real_ip": null, "via": null, "proxy_headers": []}, "geolocation_analysis": {"server_ip_geo": {"country": "Unknown", "city": "Unknown", "latitude": null, "longitude": null}, "client_timezone": null, "client_geo": null, "mismatch_detected": false}, "reputation_analysis": {"checked": false, "is_malicious": false, "is_vpn": false, "confidence": 0}, "client_analysis": []}, "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* YaBrowser/******** Safari/537.36"}, {"timestamp": 1751827938, "ip": "127.0.0.1", "risk_score": 0, "vpn_detected": false, "analysis": {"ip_analysis": {"ip": "127.0.0.1", "is_private": true, "is_reserved": true, "reverse_dns": "argun.loc", "asn_info": null}, "header_analysis": {"user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* YaBrowser/******** Safari/537.36", "accept_language": "ru,en;q=0.9", "x_forwarded_for": null, "x_real_ip": null, "via": null, "proxy_headers": []}, "geolocation_analysis": {"server_ip_geo": {"country": "Unknown", "city": "Unknown", "latitude": null, "longitude": null}, "client_timezone": null, "client_geo": null, "mismatch_detected": false}, "reputation_analysis": {"checked": false, "is_malicious": false, "is_vpn": false, "confidence": 0}, "client_analysis": []}, "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* YaBrowser/******** Safari/537.36"}, {"timestamp": 1751828065, "ip": "127.0.0.1", "risk_score": 0, "vpn_detected": false, "analysis": {"ip_analysis": {"ip": "127.0.0.1", "is_private": true, "is_reserved": true, "reverse_dns": "argun.loc", "asn_info": null}, "header_analysis": {"user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* YaBrowser/******** Safari/537.36", "accept_language": "ru,en;q=0.9", "x_forwarded_for": null, "x_real_ip": null, "via": null, "proxy_headers": []}, "geolocation_analysis": {"server_ip_geo": {"country": "Unknown", "city": "Unknown", "latitude": null, "longitude": null}, "client_timezone": null, "client_geo": null, "mismatch_detected": false}, "reputation_analysis": {"checked": false, "is_malicious": false, "is_vpn": false, "confidence": 0}, "client_analysis": []}, "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* YaBrowser/******** Safari/537.36"}, {"timestamp": 1751828067, "ip": "127.0.0.1", "risk_score": 0, "vpn_detected": false, "analysis": {"ip_analysis": {"ip": "127.0.0.1", "is_private": true, "is_reserved": true, "reverse_dns": "argun.loc", "asn_info": null}, "header_analysis": {"user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* YaBrowser/******** Safari/537.36", "accept_language": "ru,en;q=0.9", "x_forwarded_for": null, "x_real_ip": null, "via": null, "proxy_headers": []}, "geolocation_analysis": {"server_ip_geo": {"country": "Unknown", "city": "Unknown", "latitude": null, "longitude": null}, "client_timezone": null, "client_geo": null, "mismatch_detected": false}, "reputation_analysis": {"checked": false, "is_malicious": false, "is_vpn": false, "confidence": 0}, "client_analysis": []}, "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* YaBrowser/******** Safari/537.36"}, {"timestamp": 1751828074, "ip": "127.0.0.1", "risk_score": 0, "vpn_detected": false, "analysis": {"ip_analysis": {"ip": "127.0.0.1", "is_private": true, "is_reserved": true, "reverse_dns": "argun.loc", "asn_info": null}, "header_analysis": {"user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* YaBrowser/******** Safari/537.36", "accept_language": "ru,en;q=0.9", "x_forwarded_for": null, "x_real_ip": null, "via": null, "proxy_headers": []}, "geolocation_analysis": {"server_ip_geo": {"country": "Unknown", "city": "Unknown", "latitude": null, "longitude": null}, "client_timezone": null, "client_geo": null, "mismatch_detected": false}, "reputation_analysis": {"checked": false, "is_malicious": false, "is_vpn": false, "confidence": 0}, "client_analysis": []}, "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* YaBrowser/******** Safari/537.36"}, {"timestamp": 1751879963, "ip": "127.0.0.1", "risk_score": 5, "vpn_detected": false, "analysis": {"ip_analysis": {"ip": "127.0.0.1", "is_private": true, "is_reserved": true, "reverse_dns": "argun.loc", "asn_info": null}, "header_analysis": {"user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* YaBrowser/******** Safari/537.36", "accept_language": "ru,en;q=0.9", "proxy_headers": [], "suspicious_user_agent": false, "vpn_indicators": []}, "geolocation_analysis": {"server_ip_geo": {"country": "Unknown", "city": "Unknown", "latitude": null, "longitude": null}, "client_timezone": "Europe/Moscow", "client_geo": null, "mismatch_detected": true, "mismatch_details": {"ip_country": "Unknown", "timezone_country": "RU"}}, "reputation_analysis": {"checked": false, "is_malicious": false, "is_vpn": false, "confidence": 0, "country": null, "isp": null, "sources": []}, "client_analysis": {"timezone": "Europe/Moscow", "language": "ru", "platform": "Win32", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* YaBrowser/******** Safari/537.36", "screen": {"width": 1366, "height": 768, "colorDepth": 24}, "timestamp": 1751879955640}}, "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* YaBrowser/******** Safari/537.36"}, {"timestamp": 1751885294, "ip": "127.0.0.1", "risk_score": 5, "vpn_detected": false, "analysis": {"ip_analysis": {"ip": "127.0.0.1", "is_private": true, "is_reserved": true, "reverse_dns": "argun.loc", "asn_info": null}, "header_analysis": {"user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* YaBrowser/******** Safari/537.36", "accept_language": "ru,en;q=0.9", "proxy_headers": [], "suspicious_user_agent": false, "vpn_indicators": []}, "geolocation_analysis": {"server_ip_geo": {"country": "Unknown", "city": "Unknown", "latitude": null, "longitude": null}, "client_timezone": "Europe/Moscow", "client_geo": null, "mismatch_detected": true, "mismatch_details": {"ip_country": "Unknown", "timezone_country": "RU"}}, "reputation_analysis": {"checked": false, "is_malicious": false, "is_vpn": false, "confidence": 0, "country": null, "isp": null, "sources": []}, "client_analysis": {"timezone": "Europe/Moscow", "language": "ru", "platform": "Win32", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* YaBrowser/******** Safari/537.36", "screen": {"width": 1366, "height": 768, "colorDepth": 24}, "timestamp": 1751885288735}}, "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* YaBrowser/******** Safari/537.36"}, {"timestamp": 1751885376, "ip": "127.0.0.1", "risk_score": 5, "vpn_detected": false, "analysis": {"ip_analysis": {"ip": "127.0.0.1", "is_private": true, "is_reserved": true, "reverse_dns": "argun.loc", "asn_info": null}, "header_analysis": {"user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* YaBrowser/******** Safari/537.36", "accept_language": "ru,en;q=0.9", "proxy_headers": [], "suspicious_user_agent": false, "vpn_indicators": []}, "geolocation_analysis": {"server_ip_geo": {"country": "Unknown", "city": "Unknown", "latitude": null, "longitude": null}, "client_timezone": "Europe/Moscow", "client_geo": null, "mismatch_detected": true, "mismatch_details": {"ip_country": "Unknown", "timezone_country": "RU"}}, "reputation_analysis": {"checked": false, "is_malicious": false, "is_vpn": false, "confidence": 0, "country": null, "isp": null, "sources": []}, "client_analysis": {"timezone": "Europe/Moscow", "timestamp": 1751885376009}}, "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* YaBrowser/******** Safari/537.36"}]